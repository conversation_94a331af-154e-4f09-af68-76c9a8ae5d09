# Comprehensive Testing Protocol

## 1. 🧪 Unit Testing Strategy

### Backend API Unit Tests
```python
# tests/test_api_endpoints.py
import pytest
from fastapi.testclient import TestClient
from api.main import app
from api.models.generation import GenerationRequest

client = TestClient(app)

class TestTemplateEndpoints:
    def test_get_templates_success(self):
        """Test successful template retrieval"""
        response = client.get("/api/v1/templates")
        assert response.status_code == 200
        
        data = response.json()
        assert "templates" in data
        assert len(data["templates"]) > 0
        
        # Validate template structure
        template = data["templates"][0]
        required_fields = ["id", "title", "description", "industry", "compliance"]
        for field in required_fields:
            assert field in template
    
    def test_get_template_by_id(self):
        """Test specific template retrieval"""
        response = client.get("/api/v1/templates/restaurant-opening")
        assert response.status_code == 200
        
        template = response.json()
        assert template["id"] == "restaurant-opening"
        assert "regulatory_requirements" in template
    
    def test_get_nonexistent_template(self):
        """Test 404 for nonexistent template"""
        response = client.get("/api/v1/templates/nonexistent")
        assert response.status_code == 404

class TestGenerationEndpoints:
    def test_generate_sop_success(self):
        """Test successful SOP generation"""
        request_data = {
            "template_id": "restaurant-opening",
            "company_info": {
                "name": "Test Restaurant",
                "location": "Austin, TX"
            },
            "customization": {
                "selected_options": ["food-safety", "haccp"]
            },
            "llm_provider": "automatic"
        }
        
        response = client.post("/api/v1/generate", json=request_data)
        assert response.status_code == 202  # Accepted for async processing
        
        data = response.json()
        assert "generation_id" in data
        assert "websocket_url" in data
    
    def test_generate_sop_validation_error(self):
        """Test validation error handling"""
        invalid_request = {
            "template_id": "restaurant-opening",
            # Missing required company_info
            "customization": {}
        }
        
        response = client.post("/api/v1/generate", json=invalid_request)
        assert response.status_code == 422
        
        error = response.json()
        assert "error" in error
        assert "company_info" in error["error"]["message"]

class TestBrandEndpoints:
    def test_upload_logo_success(self):
        """Test successful logo upload"""
        # Create test image file
        test_image = create_test_image()
        
        response = client.post(
            "/api/v1/brand/upload-logo",
            files={"logo_file": ("test.png", test_image, "image/png")},
            data={"company_id": "test_company"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "logo_url" in data
    
    def test_update_brand_config(self):
        """Test brand configuration update"""
        config_data = {
            "primary_color": "#2C3E50",
            "secondary_color": "#3498DB",
            "company_name": "Test Company",
            "tagline": "Test Tagline"
        }
        
        response = client.put("/api/v1/brand/config", json=config_data)
        assert response.status_code == 200
```

### Frontend Component Unit Tests
```typescript
// tests/components/Generate.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Generate from '../src/pages/Generate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: { queries: { retry: false } }
});

describe('Generate Component', () => {
  test('renders template information correctly', () => {
    const queryClient = createTestQueryClient();
    
    render(
      <QueryClientProvider client={queryClient}>
        <Generate />
      </QueryClientProvider>
    );
    
    expect(screen.getByText('Restaurant Opening Procedures')).toBeInTheDocument();
    expect(screen.getByText('2-3 minutes')).toBeInTheDocument();
    expect(screen.getByText('FDA Food Code')).toBeInTheDocument();
  });
  
  test('validates required company name', async () => {
    const queryClient = createTestQueryClient();
    
    render(
      <QueryClientProvider client={queryClient}>
        <Generate />
      </QueryClientProvider>
    );
    
    const generateButton = screen.getByText('Generate SOP Template');
    expect(generateButton).toBeDisabled();
    
    // Enter company name
    const companyInput = screen.getByLabelText('Company Name *');
    fireEvent.change(companyInput, { target: { value: 'Test Company' } });
    
    await waitFor(() => {
      expect(generateButton).not.toBeDisabled();
    });
  });
  
  test('shows generation progress', async () => {
    const queryClient = createTestQueryClient();
    
    // Mock API response
    jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        generation_id: 'test-123',
        websocket_url: 'ws://localhost:8000/ws/generation/test-123'
      })
    });
    
    render(
      <QueryClientProvider client={queryClient}>
        <Generate />
      </QueryClientProvider>
    );
    
    // Fill form and submit
    fireEvent.change(screen.getByLabelText('Company Name *'), {
      target: { value: 'Test Company' }
    });
    
    fireEvent.click(screen.getByText('Generate SOP Template'));
    
    await waitFor(() => {
      expect(screen.getByText('Generating your SOP...')).toBeInTheDocument();
    });
  });
});
```

## 2. 🔗 Integration Testing

### API Integration Tests
```python
# tests/test_integration.py
import pytest
import asyncio
from httpx import AsyncClient
from api.main import app

@pytest.mark.asyncio
class TestSOPGenerationWorkflow:
    async def test_complete_sop_generation_workflow(self):
        """Test complete SOP generation from start to finish"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            # Step 1: Get available templates
            response = await client.get("/api/v1/templates")
            assert response.status_code == 200
            templates = response.json()["templates"]
            template_id = templates[0]["id"]
            
            # Step 2: Start generation
            generation_request = {
                "template_id": template_id,
                "company_info": {
                    "name": "Integration Test Restaurant",
                    "location": "Test City, TX"
                },
                "customization": {
                    "selected_options": ["food-safety"]
                },
                "llm_provider": "automatic"
            }
            
            response = await client.post("/api/v1/generate", json=generation_request)
            assert response.status_code == 202
            
            generation_data = response.json()
            generation_id = generation_data["generation_id"]
            
            # Step 3: Poll for completion (simulate WebSocket)
            max_attempts = 30  # 30 seconds timeout
            for attempt in range(max_attempts):
                response = await client.get(f"/api/v1/generate/{generation_id}/status")
                assert response.status_code == 200
                
                status_data = response.json()
                if status_data["status"] == "completed":
                    assert "result" in status_data
                    assert "pdf_url" in status_data["result"]
                    break
                elif status_data["status"] == "failed":
                    pytest.fail(f"Generation failed: {status_data.get('error')}")
                
                await asyncio.sleep(1)
            else:
                pytest.fail("Generation timed out")
            
            # Step 4: Download generated PDF
            pdf_url = status_data["result"]["pdf_url"]
            response = await client.get(pdf_url)
            assert response.status_code == 200
            assert response.headers["content-type"] == "application/pdf"
            assert len(response.content) > 1000  # Ensure PDF has content

    async def test_brand_customization_workflow(self):
        """Test brand customization and logo upload workflow"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            # Step 1: Upload logo
            test_logo = create_test_logo_bytes()
            files = {"logo_file": ("logo.png", test_logo, "image/png")}
            data = {"company_id": "test_company"}
            
            response = await client.post("/api/v1/brand/upload-logo", files=files, data=data)
            assert response.status_code == 200
            
            logo_data = response.json()
            logo_url = logo_data["logo_url"]
            
            # Step 2: Update brand configuration
            brand_config = {
                "primary_color": "#FF5733",
                "secondary_color": "#33FF57",
                "company_name": "Test Brand Company",
                "tagline": "Testing Excellence",
                "logo_url": logo_url
            }
            
            response = await client.put("/api/v1/brand/config", json=brand_config)
            assert response.status_code == 200
            
            # Step 3: Generate SOP with custom branding
            generation_request = {
                "template_id": "restaurant-opening",
                "company_info": {"name": "Test Brand Company"},
                "customization": {"brand_config": brand_config}
            }
            
            response = await client.post("/api/v1/generate", json=generation_request)
            assert response.status_code == 202
            
            # Verify generation includes custom branding
            generation_id = response.json()["generation_id"]
            # ... poll for completion and verify branding in result
```

### Frontend-Backend Integration Tests
```typescript
// tests/integration/sop-generation.test.ts
import { test, expect } from '@playwright/test';

test.describe('SOP Generation Integration', () => {
  test('complete SOP generation workflow', async ({ page }) => {
    // Navigate to application
    await page.goto('/');
    
    // Select template
    await page.click('[data-testid="restaurant-template"]');
    await expect(page).toHaveURL(/\/generate\/restaurant/);
    
    // Fill company information
    await page.fill('[data-testid="company-name"]', 'Playwright Test Restaurant');
    await page.fill('[data-testid="location"]', 'Austin, TX');
    
    // Select customization options
    await page.check('[data-testid="option-food-safety"]');
    await page.check('[data-testid="option-haccp"]');
    
    // Start generation
    await page.click('[data-testid="generate-button"]');
    
    // Wait for generation to complete
    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();
    await expect(page.locator('[data-testid="generation-complete"]')).toBeVisible({ timeout: 60000 });
    
    // Verify PDF download link
    const downloadLink = page.locator('[data-testid="download-pdf"]');
    await expect(downloadLink).toBeVisible();
    
    // Test PDF download
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      downloadLink.click()
    ]);
    
    expect(download.suggestedFilename()).toMatch(/\.pdf$/);
  });
  
  test('brand customization workflow', async ({ page }) => {
    await page.goto('/brand');
    
    // Upload logo
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('tests/fixtures/test-logo.png');
    
    // Wait for upload completion
    await expect(page.locator('[data-testid="logo-preview"]')).toBeVisible();
    
    // Update brand colors
    await page.fill('[data-testid="primary-color"]', '#FF5733');
    await page.fill('[data-testid="secondary-color"]', '#33FF57');
    
    // Save configuration
    await page.click('[data-testid="save-brand-config"]');
    await expect(page.locator('[data-testid="save-success"]')).toBeVisible();
    
    // Generate SOP with custom branding
    await page.goto('/generate/restaurant');
    await page.fill('[data-testid="company-name"]', 'Branded Restaurant');
    await page.click('[data-testid="generate-button"]');
    
    // Verify custom branding in preview
    await expect(page.locator('[data-testid="pdf-preview"]')).toBeVisible();
    // Additional assertions for custom colors/logo in preview
  });
});
```

## 3. 🔄 End-to-End Testing

### Complete User Journey Tests
```typescript
// tests/e2e/user-journey.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Complete User Journey', () => {
  test('new user registration to SOP generation', async ({ page }) => {
    // User registration
    await page.goto('/register');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'SecurePassword123');
    await page.fill('[data-testid="company-name"]', 'E2E Test Company');
    await page.click('[data-testid="register-button"]');
    
    // Email verification (mock)
    await expect(page.locator('[data-testid="verification-sent"]')).toBeVisible();
    
    // Login
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'SecurePassword123');
    await page.click('[data-testid="login-button"]');
    
    // Dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('E2E Test Company');
    
    // Browse templates
    await page.click('[data-testid="browse-templates"]');
    await expect(page.locator('[data-testid="template-grid"]')).toBeVisible();
    
    // Generate first SOP
    await page.click('[data-testid="restaurant-template"]');
    await page.fill('[data-testid="company-name"]', 'E2E Test Restaurant');
    await page.click('[data-testid="generate-button"]');
    
    // Wait for completion and download
    await expect(page.locator('[data-testid="generation-complete"]')).toBeVisible({ timeout: 60000 });
    
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.click('[data-testid="download-pdf"]')
    ]);
    
    expect(download.suggestedFilename()).toMatch(/Restaurant.*\.pdf$/);
    
    // Verify document in library
    await page.goto('/documents');
    await expect(page.locator('[data-testid="document-list"]')).toContainText('E2E Test Restaurant');
  });
  
  test('batch generation workflow', async ({ page }) => {
    await page.goto('/batch');
    
    // Add multiple templates to batch
    await page.click('[data-testid="add-template"]');
    await page.selectOption('[data-testid="template-select"]', 'restaurant-opening');
    await page.fill('[data-testid="batch-company-name"]', 'Batch Restaurant 1');
    await page.click('[data-testid="add-to-batch"]');
    
    await page.click('[data-testid="add-template"]');
    await page.selectOption('[data-testid="template-select"]', 'restaurant-closing');
    await page.fill('[data-testid="batch-company-name"]', 'Batch Restaurant 2');
    await page.click('[data-testid="add-to-batch"]');
    
    // Start batch generation
    await page.click('[data-testid="start-batch"]');
    
    // Monitor batch progress
    await expect(page.locator('[data-testid="batch-progress"]')).toBeVisible();
    await expect(page.locator('[data-testid="batch-complete"]')).toBeVisible({ timeout: 120000 });
    
    // Verify all documents generated
    const completedItems = page.locator('[data-testid="batch-item"][data-status="completed"]');
    await expect(completedItems).toHaveCount(2);
  });
});
```

## 4. ⚡ Performance Testing

### Load Testing
```python
# tests/performance/load_test.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class LoadTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.results = []
    
    async def test_concurrent_generations(self, concurrent_users=10, requests_per_user=5):
        """Test concurrent SOP generations"""
        
        async def user_session(user_id):
            async with aiohttp.ClientSession() as session:
                user_results = []
                
                for request_num in range(requests_per_user):
                    start_time = time.time()
                    
                    try:
                        # Start generation
                        generation_data = {
                            "template_id": "restaurant-opening",
                            "company_info": {
                                "name": f"Load Test Restaurant {user_id}-{request_num}"
                            }
                        }
                        
                        async with session.post(
                            f"{self.base_url}/api/v1/generate",
                            json=generation_data
                        ) as response:
                            if response.status == 202:
                                result = await response.json()
                                generation_id = result["generation_id"]
                                
                                # Poll for completion
                                completed = await self.wait_for_completion(session, generation_id)
                                
                                end_time = time.time()
                                user_results.append({
                                    "user_id": user_id,
                                    "request_num": request_num,
                                    "duration": end_time - start_time,
                                    "success": completed,
                                    "status_code": response.status
                                })
                            else:
                                end_time = time.time()
                                user_results.append({
                                    "user_id": user_id,
                                    "request_num": request_num,
                                    "duration": end_time - start_time,
                                    "success": False,
                                    "status_code": response.status
                                })
                    
                    except Exception as e:
                        end_time = time.time()
                        user_results.append({
                            "user_id": user_id,
                            "request_num": request_num,
                            "duration": end_time - start_time,
                            "success": False,
                            "error": str(e)
                        })
                
                return user_results
        
        # Run concurrent user sessions
        tasks = [user_session(i) for i in range(concurrent_users)]
        all_results = await asyncio.gather(*tasks)
        
        # Flatten results
        for user_results in all_results:
            self.results.extend(user_results)
        
        return self.analyze_results()
    
    def analyze_results(self):
        """Analyze load test results"""
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r["success"])
        failed_requests = total_requests - successful_requests
        
        durations = [r["duration"] for r in self.results if r["success"]]
        
        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": successful_requests / total_requests * 100,
            "average_duration": sum(durations) / len(durations) if durations else 0,
            "min_duration": min(durations) if durations else 0,
            "max_duration": max(durations) if durations else 0,
            "requests_per_second": total_requests / max(durations) if durations else 0
        }

# Run load test
async def main():
    tester = LoadTester()
    results = await tester.test_concurrent_generations(concurrent_users=20, requests_per_user=3)
    
    print("Load Test Results:")
    print(f"Success Rate: {results['success_rate']:.2f}%")
    print(f"Average Duration: {results['average_duration']:.2f}s")
    print(f"Requests per Second: {results['requests_per_second']:.2f}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 5. 🔒 Security Testing

### Authentication & Authorization Tests
```python
# tests/security/auth_test.py
import pytest
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)

class TestSecurityFeatures:
    def test_unauthenticated_access_denied(self):
        """Test that protected endpoints require authentication"""
        protected_endpoints = [
            "/api/v1/generate",
            "/api/v1/documents",
            "/api/v1/brand/config"
        ]
        
        for endpoint in protected_endpoints:
            response = client.get(endpoint)
            assert response.status_code == 401
    
    def test_jwt_token_validation(self):
        """Test JWT token validation"""
        # Test with invalid token
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/documents", headers=headers)
        assert response.status_code == 401
        
        # Test with expired token
        expired_token = create_expired_jwt_token()
        headers = {"Authorization": f"Bearer {expired_token}"}
        response = client.get("/api/v1/documents", headers=headers)
        assert response.status_code == 401
    
    def test_file_upload_security(self):
        """Test file upload security measures"""
        # Test malicious file upload
        malicious_file = create_malicious_file()
        response = client.post(
            "/api/v1/brand/upload-logo",
            files={"logo_file": ("malicious.exe", malicious_file, "application/exe")}
        )
        assert response.status_code == 400
        
        # Test oversized file
        large_file = b"x" * (10 * 1024 * 1024)  # 10MB
        response = client.post(
            "/api/v1/brand/upload-logo",
            files={"logo_file": ("large.png", large_file, "image/png")}
        )
        assert response.status_code == 413
    
    def test_sql_injection_protection(self):
        """Test SQL injection protection"""
        malicious_input = "'; DROP TABLE users; --"
        
        response = client.get(f"/api/v1/templates/{malicious_input}")
        assert response.status_code in [400, 404]  # Should not cause server error
    
    def test_xss_protection(self):
        """Test XSS protection in user inputs"""
        xss_payload = "<script>alert('xss')</script>"
        
        generation_request = {
            "template_id": "restaurant-opening",
            "company_info": {
                "name": xss_payload
            }
        }
        
        # Should sanitize input, not execute script
        response = client.post("/api/v1/generate", json=generation_request)
        # Verify response doesn't contain unsanitized script
```

## 6. 📋 Regulatory Compliance Testing

### FDA Food Code Compliance Validation
```python
# tests/compliance/fda_compliance_test.py
class TestFDACompliance:
    def test_restaurant_sop_fda_compliance(self):
        """Test that restaurant SOPs meet FDA Food Code requirements"""
        
        # Generate restaurant SOP
        sop_data = generate_test_sop("restaurant-opening")
        
        # Validate required FDA sections
        required_sections = [
            "hand_washing_procedures",
            "temperature_monitoring",
            "allergen_management",
            "cleaning_sanitizing"
        ]
        
        for section in required_sections:
            assert section in sop_data["sections"]
            assert len(sop_data["sections"][section]["content"]) > 100
        
        # Validate regulatory citations
        citations = sop_data["regulatory_citations"]
        fda_citations = [c for c in citations if "FDA Food Code" in c["regulation"]]
        assert len(fda_citations) >= 3
        
        # Validate specific requirements
        hand_washing = sop_data["sections"]["hand_washing_procedures"]
        assert "20 seconds" in hand_washing["content"]
        assert "soap and water" in hand_washing["content"]
```

This comprehensive testing protocol ensures that your SOP Builder MVP integration maintains the highest quality standards while preserving all existing functionality and meeting commercial deployment requirements.
