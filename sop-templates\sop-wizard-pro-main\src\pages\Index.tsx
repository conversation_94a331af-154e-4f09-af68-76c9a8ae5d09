
import { <PERSON> } from "react-router-dom";
import { ArrowRight, FileText, Users, ShieldCheck, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Layout from "@/components/Layout";

const Index = () => {
  const templateCategories = [
    {
      id: "restaurant",
      title: "Restaurant Operations",
      description: "Food safety, HACCP compliance, staff training procedures",
      icon: "🍽️",
      count: 12,
      compliance: ["FDA Food Code", "HACCP", "ServSafe"]
    },
    {
      id: "healthcare",
      title: "Healthcare Procedures",
      description: "Patient care protocols, safety procedures, HIPAA compliance",
      icon: "🏥",
      count: 18,
      compliance: ["HIPAA", "CDC Guidelines", "Joint Commission"]
    },
    {
      id: "it-onboarding",
      title: "IT Onboarding",
      description: "Employee setup, security protocols, system access procedures",
      icon: "💻",
      count: 8,
      compliance: ["SOX", "ISO 27001", "GDPR"]
    },
    {
      id: "customer-service",
      title: "Customer Service",
      description: "Support procedures, escalation protocols, quality standards",
      icon: "📞",
      count: 10,
      compliance: ["ISO 9001", "CSAT Standards"]
    }
  ];

  const systemStatus = [
    { provider: "Groq", status: "operational", model: "llama-3.1-70b-versatile" },
    { provider: "Hugging Face", status: "operational", model: "Various OSS Models" },
    { provider: "Together AI", status: "operational", model: "Free Tier Active" },
    { provider: "OpenRouter", status: "operational", model: "deepseek-chat" }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-[#2C3E50] mb-4">
          Professional SOP Templates
        </h1>
        <p className="text-xl text-gray-600 mb-6 max-w-3xl mx-auto">
          Generate industry-compliant Standard Operating Procedures using free AI providers. 
          Professional formatting, regulatory compliance, and zero subscription costs.
        </p>
        <div className="flex justify-center space-x-4">
          <Button asChild size="lg" className="bg-[#3498DB] hover:bg-[#2980B9]">
            <Link to="/templates">
              Browse Templates <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </Button>
          <Button variant="outline" size="lg">
            View Documentation
          </Button>
        </div>
      </div>

      {/* Template Categories */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-[#2C3E50] mb-6">Template Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {templateCategories.map((category) => (
            <Card key={category.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
              <Link to={`/generate/${category.id}`}>
                <CardHeader className="text-center">
                  <div className="text-4xl mb-2">{category.icon}</div>
                  <CardTitle className="text-lg group-hover:text-[#3498DB] transition-colors">
                    {category.title}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {category.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#3498DB] mb-2">{category.count}</div>
                    <div className="text-xs text-gray-500 mb-3">Templates Available</div>
                    <div className="flex flex-wrap gap-1">
                      {category.compliance.map((standard) => (
                        <span key={standard} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                          {standard}
                        </span>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Link>
            </Card>
          ))}
        </div>
      </div>

      {/* Features Section */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-[#2C3E50] mb-6">Key Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="text-center">
              <Zap className="w-8 h-8 text-[#E74C3C] mx-auto mb-2" />
              <CardTitle className="text-lg">100% Free AI</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 text-center">
                Multiple free LLM providers with automatic fallback ensure zero costs
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <FileText className="w-8 h-8 text-[#E74C3C] mx-auto mb-2" />
              <CardTitle className="text-lg">Professional PDFs</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 text-center">
                Beautifully formatted documents with proper typography and branding
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <ShieldCheck className="w-8 h-8 text-[#E74C3C] mx-auto mb-2" />
              <CardTitle className="text-lg">Compliance Ready</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 text-center">
                Industry-specific compliance standards and regulatory citations
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <Users className="w-8 h-8 text-[#E74C3C] mx-auto mb-2" />
              <CardTitle className="text-lg">Team Ready</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 text-center">
                Multiple user support with role-based access and collaboration
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* System Status */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-[#2C3E50] mb-6">System Status</h2>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              All Systems Operational
            </CardTitle>
            <CardDescription>
              All LLM providers are functioning normally. Average response time: 2.3 seconds
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {systemStatus.map((provider) => (
                <div key={provider.provider} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{provider.provider}</span>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-600">{provider.model}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Index;
