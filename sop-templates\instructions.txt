You are helping me build an automated SOP (Standard Operating Procedure) template generation system. The project is located at C:\Projects\SOP-Builder-MVP\sop-templates\ and the basic structure has been created.

**Project Goal**: Create a minimal viable product that can generate, design, and sell SOP templates automatically using AI, with only 1 hour/day maintenance.

**Current Status**: 
- Basic directory structure exists
- Core Python scripts are scaffolded but need implementation
- Requirements.txt is ready
- Environment variables template exists

**Your Tasks**:

1. **Environment Setup** (First Priority):
   - Create a virtual environment in the project directory
   - Install all dependencies from requirements.txt
   - Create a .env file from .env.example with placeholder values
   - Add helpful comments for each environment variable

2. **Implement Core Functionality**:
   
   a) **Fix and optimize sop_generator.py**:
      - Implement error handling for API failures
      - Add retry logic with exponential backoff
      - Create a template caching system to reduce API calls
      - Add progress indicators for long-running operations
      - Implement section validation to ensure all required content is generated
   
   b) **Enhance pdf_generator.py**:
      - Add fallback fonts for cross-platform compatibility
      - Implement dynamic page numbering
      - Create a table of contents with clickable links
      - Add watermark support for demo versions
      - Implement batch PDF generation
   
   c) **Simplify video_generator.py**:
      - Remove complex MoviePy dependencies
      - Implement a simple slideshow generator using PIL only
      - Create static image templates for each template type
      - Generate a simple MP4 using ffmpeg command line
      - Add error handling for missing assets

3. **Create Missing Data Files**:
   - Generate compliance YAML files for: healthcare, it-onboarding, customer-service
   - Create prompt JSON files for each template type
   - Build a master template configuration file
   - Add sample brand assets (create placeholder images)

4. **Build Testing Framework**:
   - Create tests/ directory with __init__.py
   - Implement unit tests for each generator
   - Add integration tests for the full pipeline
   - Create mock responses for API calls
   - Add a test data directory with sample outputs

5. **Implement MVP Web Interface**:
   - Create a simple Flask app in app.py
   - Build routes for:
     - GET / - Home page with template previews
     - GET /templates/<type> - View specific template details
     - POST /generate - Trigger template generation
     - GET /download/<type> - Download generated PDF
   - Use simple HTML templates with inline CSS (no complex frontend)
   - Add basic authentication with hardcoded credentials for now

6. **Create Automation Scripts**:
   - Write a simple cron-compatible script for daily updates
   - Implement a health check endpoint
   - Create a deployment script for Render.com or Railway
   - Add logging to a centralized file

7. **Documentation**:
   - Update README.md with actual working commands
   - Create a docs/ folder with:
     - API.md - Document all endpoints
     - DEPLOYMENT.md - Step-by-step deployment guide
     - TROUBLESHOOTING.md - Common issues and solutions
   - Add inline code documentation

8. **Quick Wins for MVP**:
   - Hardcode template content for faster testing (remove AI dependency initially)
   - Use local file storage instead of cloud services
   - Implement a simple SQLite database for tracking generations
   - Create a basic admin panel at /admin

**Code Style Requirements**:
- Use type hints for all functions
- Add docstrings to all classes and functions
- Follow PEP 8 conventions
- Use meaningful variable names
- Add TODO comments for future enhancements

**Testing Each Component**:
After implementing each file, create a corresponding test file and verify it works independently before moving to the next component.

**Priority Order**:
1. Get sop_generator.py working with hardcoded content
2. Make pdf_generator.py produce a valid PDF
3. Create the Flask web interface
4. Add the database layer
5. Implement the automation scripts
6. Add AI integration as the final step

Please start with step 1 (Environment Setup) and then proceed through each task systematically. For each file you modify or create, show me the complete code and explain the key implementation decisions.

Focus on getting a working MVP that can generate one template type (restaurant) end-to-end before adding complexity. We can iterate and improve after the basic pipeline works.
Additional Context for Augment
When using this prompt, you can also add these follow-up instructions based on what Augment is working on:
For Code Generation:
When implementing each function, please:
1. Add comprehensive error handling
2. Include logging statements for debugging
3. Write unit tests immediately after the function
4. Add performance considerations in comments
For Debugging:
If you encounter any errors:
1. Show me the full error traceback
2. Explain what might be causing the issue
3. Propose 2-3 different solutions
4. Implement the most pragmatic fix for MVP
For Optimization:
After the basic implementation works:
1. Identify the slowest operations
2. Suggest caching strategies
3. Recommend async operations where beneficial
4. Add progress bars for user feedback