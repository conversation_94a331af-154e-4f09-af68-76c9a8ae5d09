@echo off
REM Windows batch file for quick SOP template tasks

echo SOP Template Automation System
echo ==============================
echo.

if "%1"=="" goto help

if "%1"=="setup" goto setup
if "%1"=="daily" goto daily
if "%1"=="generate" goto generate
if "%1"=="pdf" goto pdf
if "%1"=="deploy" goto deploy
if "%1"=="clean" goto clean
goto help

:setup
echo Setting up SOP Template System...
python make.py setup
goto end

:daily
echo Running daily workflow...
python make.py daily
goto end

:generate
echo Generating templates...
if "%2"=="" (
    python make.py generate
) else (
    python make.py generate --type %2
)
goto end

:pdf
echo Creating PDFs...
python make.py pdf
goto end

:deploy
echo Deploying to Gumroad...
if "%2"=="test" (
    python make.py deploy --test
) else (
    python make.py deploy
)
goto end

:clean
echo Cleaning temporary files...
python make.py clean
goto end

:help
echo Usage: make.bat [command] [options]
echo.
echo Commands:
echo   setup     - Initial system setup
echo   daily     - Run complete daily workflow
echo   generate  - Generate SOP templates (optional: type)
echo   pdf       - Convert templates to PDF
echo   deploy    - Deploy to Gumroad (optional: test)
echo   clean     - Clean temporary files
echo.
echo Examples:
echo   make.bat setup
echo   make.bat daily
echo   make.bat generate restaurant
echo   make.bat deploy test

:end
