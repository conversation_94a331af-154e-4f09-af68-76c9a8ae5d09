# Backend Modifications Plan

## 1. 🏗️ New Backend Architecture

### FastAPI Web Server Integration
Create a new FastAPI application that wraps existing Python scripts:

```
sop-templates/
├── api/                          # New FastAPI application
│   ├── __init__.py
│   ├── main.py                   # FastAPI app entry point
│   ├── routers/                  # API route handlers
│   │   ├── __init__.py
│   │   ├── templates.py          # Template management
│   │   ├── generation.py         # SOP generation
│   │   ├── documents.py          # Document management
│   │   ├── brand.py              # Brand customization
│   │   └── compliance.py         # Regulatory validation
│   ├── models/                   # Pydantic models
│   │   ├── __init__.py
│   │   ├── templates.py
│   │   ├── generation.py
│   │   └── brand.py
│   ├── services/                 # Business logic
│   │   ├── __init__.py
│   │   ├── sop_service.py        # Wraps existing sop_generator.py
│   │   ├── pdf_service.py        # Wraps existing pdf_generator.py
│   │   └── llm_service.py        # Wraps existing llm_client.py
│   ├── database/                 # Database layer
│   │   ├── __init__.py
│   │   ├── models.py             # SQLAlchemy models
│   │   └── connection.py         # Database connection
│   └── utils/                    # Utilities
│       ├── __init__.py
│       ├── auth.py               # JWT authentication
│       └── websockets.py         # Real-time updates
```

### Key Integration Points

#### 1. SOP Generation Service (api/services/sop_service.py)
```python
from scripts.generators.sop_generator import SOPGenerator
from scripts.utils.llm_client import FreeLLMClient

class SOPGenerationService:
    def __init__(self):
        self.sop_generator = SOPGenerator()
        self.llm_client = FreeLLMClient()
    
    async def generate_sop_async(self, request: GenerationRequest):
        """Async wrapper for existing SOP generation"""
        # Preserve existing LLM provider logic
        # Add WebSocket progress updates
        # Return structured response
```

#### 2. PDF Service (api/services/pdf_service.py)
```python
from scripts.generators.pdf_generator import EnhancedSOPPDFGenerator

class PDFGenerationService:
    def __init__(self):
        self.pdf_generator = EnhancedSOPPDFGenerator()
    
    async def generate_pdf_async(self, template_data, brand_config):
        """Async wrapper for existing PDF generation"""
        # Preserve existing PDF quality and formatting
        # Add progress tracking
        # Return file metadata
```

## 2. 🗄️ Database Schema

### SQLite Database (Development) / PostgreSQL (Production)

```sql
-- Users table
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Brand configurations
CREATE TABLE brand_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    primary_color VARCHAR(7) DEFAULT '#2C3E50',
    secondary_color VARCHAR(7) DEFAULT '#3498DB',
    company_name VARCHAR(255),
    tagline VARCHAR(255),
    logo_path VARCHAR(500),
    font_family VARCHAR(100) DEFAULT 'Helvetica',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Generated documents
CREATE TABLE documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    template_id VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    generation_params JSON,
    status VARCHAR(50) DEFAULT 'completed',
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Generation jobs (for async processing)
CREATE TABLE generation_jobs (
    id VARCHAR(100) PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    template_id VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    current_step VARCHAR(255),
    request_data JSON,
    result_data JSON,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Template usage analytics
CREATE TABLE template_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_id VARCHAR(100) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    generation_time_seconds REAL,
    llm_provider VARCHAR(50),
    success BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 3. 🔧 Existing Script Adaptations

### Minimal Changes to Preserve Functionality

#### A. LLM Client Integration (scripts/utils/llm_client.py)
```python
# Add async support while preserving existing sync methods
class FreeLLMClient:
    # Existing methods remain unchanged
    
    async def generate_async(self, system_prompt: str, user_prompt: str, 
                           progress_callback=None) -> LLMResponse:
        """Async version with progress callbacks"""
        # Wrap existing generate() method
        # Add progress updates via callback
        return await asyncio.to_thread(
            self.generate, system_prompt, user_prompt
        )
```

#### B. SOP Generator Enhancement (scripts/generators/sop_generator.py)
```python
# Add progress tracking without breaking existing functionality
class SOPGenerator:
    # Existing methods remain unchanged
    
    def generate_with_progress(self, template_type: str, 
                             progress_callback=None, **kwargs):
        """Enhanced version with progress tracking"""
        # Wrap existing generate_sop method
        # Add progress callbacks at key steps
        # Preserve all existing caching and error handling
```

#### C. PDF Generator Enhancement (scripts/generators/pdf_generator.py)
```python
# Add web-friendly features while preserving quality
class EnhancedSOPPDFGenerator:
    # Existing methods remain unchanged
    
    def generate_with_preview(self, template_data, output_path=None):
        """Generate PDF with preview capability"""
        # Use existing generate_enhanced_pdf method
        # Add base64 preview generation
        # Preserve all formatting and branding
```

## 4. 🔐 Authentication & Session Management

### JWT-based Authentication
```python
# api/utils/auth.py
from jose import JWTError, jwt
from passlib.context import CryptContext

class AuthService:
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"])
        self.secret_key = os.getenv("JWT_SECRET_KEY")
        self.algorithm = "HS256"
    
    def create_access_token(self, data: dict):
        """Create JWT token for user session"""
        
    def verify_token(self, token: str):
        """Verify and decode JWT token"""
```

### Session Management
- JWT tokens with 24-hour expiration
- Refresh token mechanism
- Rate limiting per user
- Session-based document access control

## 5. 🚀 Async Task Processing

### Background Job Processing
```python
# api/services/background_tasks.py
from celery import Celery
import asyncio

app = Celery('sop_builder')

@app.task
def generate_sop_task(generation_id: str, request_data: dict):
    """Background task for SOP generation"""
    # Use existing SOPGenerator
    # Update database with progress
    # Send WebSocket updates
    # Generate PDF automatically
```

### WebSocket Progress Updates
```python
# api/utils/websockets.py
from fastapi import WebSocket

class GenerationProgressManager:
    def __init__(self):
        self.active_connections = {}
    
    async def connect(self, websocket: WebSocket, generation_id: str):
        """Connect client to generation progress updates"""
        
    async def send_progress(self, generation_id: str, progress: dict):
        """Send progress update to connected clients"""
```

## 6. 📁 File Management

### Secure File Storage
```python
# api/services/file_service.py
class FileService:
    def __init__(self):
        self.upload_dir = Path("uploads")
        self.output_dir = Path("outputs")
    
    async def save_logo(self, file_data: bytes, user_id: int) -> str:
        """Save uploaded logo securely"""
        # Validate file type and size
        # Generate secure filename
        # Update brand configuration
    
    async def get_document(self, document_id: str, user_id: int) -> Path:
        """Get document with access control"""
        # Verify user owns document
        # Return secure file path
```

## 7. 🔄 Migration Strategy

### Phase 1: Core API (Week 1-2)
1. Set up FastAPI application structure
2. Create database models and migrations
3. Implement basic authentication
4. Wrap existing SOP generator with async API

### Phase 2: Frontend Integration (Week 3-4)
1. Implement template management endpoints
2. Add brand configuration API
3. Create document management system
4. Set up WebSocket progress updates

### Phase 3: Advanced Features (Week 5-6)
1. Add compliance validation endpoints
2. Implement batch processing
3. Create analytics and reporting
4. Add file upload and management

### Phase 4: Production Deployment (Week 7-8)
1. Set up production database
2. Configure SSL and security
3. Implement monitoring and logging
4. Deploy to nextlevelsbs.com

## 8. 🧪 Testing Strategy

### Preserve Existing Functionality
- All existing Python scripts must continue working
- Existing command-line interfaces preserved
- Current PDF quality maintained
- LLM provider fallback logic intact

### New API Testing
- Unit tests for all API endpoints
- Integration tests for SOP generation workflow
- Performance tests for concurrent users
- Security tests for authentication and file access
