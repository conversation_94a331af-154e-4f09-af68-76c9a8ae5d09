# =============================================================================
# SOP BUILDER MVP - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual API keys and configuration

# =============================================================================
# AI CONTENT GENERATION
# =============================================================================

# OpenAI API for content generation (GPT-4 for high-quality SOP content)
# Get your API key from: https://platform.openai.com/api-keys
# Required for: SOP content generation, section writing, compliance text
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude API for advanced content (alternative to OpenAI)
# Get your API key from: https://console.anthropic.com/
# Optional: Used as fallback or for specific content types
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# DATA MANAGEMENT & STORAGE
# =============================================================================

# Google Sheets for compliance data management
# Create service account at: https://console.cloud.google.com/
# Download JSON credentials and place in ./config/google_credentials.json
GOOGLE_SHEETS_CREDENTIALS_PATH=./config/google_credentials.json

# Google Sheet ID containing compliance requirements and industry data
# Extract from sheet URL: https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit
COMPLIANCE_SHEET_ID=your_google_sheet_id_here

# =============================================================================
# DESIGN & BRANDING
# =============================================================================

# Canva API for automated design generation
# Get API access from: https://www.canva.com/developers/
# Used for: Template covers, branded elements, marketing materials
CANVA_API_KEY=your_canva_api_key_here

# Your Canva Brand Kit ID for consistent branding
# Find in Canva Brand Kit settings
CANVA_BRAND_KIT_ID=your_brand_kit_id_here

# =============================================================================
# SALES & DISTRIBUTION
# =============================================================================

# Gumroad API for automated product distribution
# Get access token from: https://gumroad.com/settings/advanced
# Used for: Uploading products, managing sales, customer delivery
GUMROAD_ACCESS_TOKEN=your_gumroad_token_here

# Gumroad Product IDs for each template type (JSON format)
# Create products first, then add their IDs here
GUMROAD_PRODUCT_IDS={"restaurant": "prod_id_1", "healthcare": "prod_id_2"}

# =============================================================================
# EMAIL & MARKETING
# =============================================================================

# Mailchimp API for customer communication
# Get API key from: https://mailchimp.com/help/about-api-keys/
# Used for: Customer notifications, marketing campaigns, updates
MAILCHIMP_API_KEY=your_mailchimp_key_here

# Mailchimp Audience/List ID for SOP customers
# Find in Audience settings
MAILCHIMP_LIST_ID=your_list_id_here

# =============================================================================
# VIDEO GENERATION (OPTIONAL)
# =============================================================================

# Synthesia API for AI video generation
# Get API access from: https://www.synthesia.io/
# Used for: Creating training videos from SOP content
SYNTHESIA_API_KEY=your_synthesia_key_here

# HeyGen API for alternative video generation
# Get API access from: https://heygen.com/
# Used as backup for video creation
HEYGEN_API_KEY=your_heygen_key_here

# =============================================================================
# MONITORING & ALERTS
# =============================================================================

# Slack webhook for system notifications
# Create webhook at: https://api.slack.com/messaging/webhooks
# Used for: Error alerts, generation completion, daily reports
SLACK_WEBHOOK_URL=your_slack_webhook_here

# Email address for error notifications
# Used when Slack is unavailable or for critical alerts
ERROR_EMAIL=<EMAIL>

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment mode (development/staging/production)
ENVIRONMENT=development

# Enable debug logging and verbose output
DEBUG=True

# Local file storage path for generated content
LOCAL_STORAGE_PATH=./outputs

# Maximum API retries for failed requests
MAX_API_RETRIES=3

# Cache duration for generated content (in hours)
CACHE_DURATION_HOURS=24
