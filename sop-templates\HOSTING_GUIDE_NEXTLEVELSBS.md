# 🌐 SOP Builder MVP - Hosting Guide for nextlevelsbs.com

## 🎯 **Quick Start Deployment (Recommended)**

### **Step 1: Build Your Application**

Run the deployment script to prepare your files:

**Windows:**
```cmd
cd sop-templates
deploy.bat
```

**Mac/Linux:**
```bash
cd sop-templates
./deploy.sh
```

This creates a `deployment-[timestamp]` folder with all your files ready for upload.

### **Step 2: Upload Frontend to Hostinger**

1. **Login to Hostinger Control Panel**
   - Go to https://hpanel.hostinger.com
   - Login with your credentials

2. **Access File Manager**
   - Navigate to "Files" → "File Manager"
   - Go to `public_html` (your website root)

3. **Create SOP Directory**
   - Create new folder: `public_html/sop/`
   - This will make your app accessible at `nextlevelsbs.com/sop/`

4. **Upload Frontend Files**
   - Upload ALL contents from `deployment-[timestamp]/frontend/` 
   - Upload to: `public_html/sop/`
   - Make sure `index.html` is directly in the `sop` folder

**File structure should look like:**
```
public_html/
├── sop/
│   ├── index.html          ← Main app file
│   ├── assets/
│   │   ├── index-[hash].js ← JavaScript bundle
│   │   ├── index-[hash].css ← Styles
│   │   └── ...
│   └── ...
└── (your existing website files)
```

### **Step 3: Deploy Backend API**

You have several options for hosting the Python backend:

## 🚀 **Option A: Railway (Recommended - Free)**

**Why Railway?**
- ✅ Free tier available
- ✅ Easy GitHub integration
- ✅ Automatic deployments
- ✅ Built-in SSL

**Setup Steps:**

1. **Create GitHub Repository**
   ```bash
   # If you haven't already, push your code to GitHub
   git init
   git add .
   git commit -m "Initial SOP Builder MVP"
   git remote add origin https://github.com/yourusername/SOP-Builder-MVP.git
   git push -u origin main
   ```

2. **Deploy to Railway**
   - Go to https://railway.app
   - Sign up with GitHub
   - Click "New Project" → "Deploy from GitHub repo"
   - Select your SOP-Builder-MVP repository
   - Railway will auto-detect Python and deploy

3. **Configure Environment**
   - In Railway dashboard, go to your project
   - Click "Variables" tab
   - Add environment variables:
     ```
     OPENROUTER_API_KEY=your_openrouter_key_here
     PORT=8000
     ```

4. **Get Your API URL**
   - Railway will provide a URL like: `https://your-app-name.railway.app`
   - Your API will be accessible at: `https://your-app-name.railway.app/api/health`

## 🌊 **Option B: Render (Alternative Free)**

1. **Connect GitHub to Render**
   - Go to https://render.com
   - Sign up with GitHub
   - Click "New" → "Web Service"
   - Connect your repository

2. **Configure Service**
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `python api/simple_server.py`
   - **Environment**: Python 3

3. **Set Environment Variables**
   - Add your OpenRouter API key
   - Set PORT=8000

## 💻 **Option C: DigitalOcean Droplet (Professional)**

**Cost**: $6/month for 1GB RAM droplet

1. **Create Droplet**
   - Go to https://digitalocean.com
   - Create Ubuntu 22.04 droplet
   - Choose $6/month plan

2. **Server Setup**
   ```bash
   # Connect via SSH
   ssh root@your_droplet_ip
   
   # Update system
   apt update && apt upgrade -y
   
   # Install Python and dependencies
   apt install -y python3 python3-pip python3-venv git nginx
   
   # Create app user
   useradd -m -s /bin/bash sopbuilder
   su - sopbuilder
   
   # Clone your repository
   git clone https://github.com/yourusername/SOP-Builder-MVP.git
   cd SOP-Builder-MVP/sop-templates
   
   # Set up Python environment
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   
   # Test the server
   python api/simple_server.py
   ```

3. **Configure Nginx (Optional)**
   ```nginx
   # /etc/nginx/sites-available/sopbuilder
   server {
       listen 80;
       server_name your_droplet_ip;
       
       location /api/ {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 🔗 **Step 4: Connect Frontend to Backend**

After deploying your backend, you need to update the frontend to use the production API URL.

1. **Update Environment File**
   ```bash
   # Edit sop-wizard-pro-main/.env.production
   VITE_API_BASE_URL=https://your-railway-app.railway.app
   # OR
   VITE_API_BASE_URL=https://your-render-app.onrender.com
   # OR  
   VITE_API_BASE_URL=http://your_droplet_ip:8000
   ```

2. **Rebuild Frontend**
   ```bash
   cd sop-wizard-pro-main
   npm run build
   ```

3. **Re-upload to Hostinger**
   - Upload the new `dist/` contents to `public_html/sop/`

## 🧪 **Step 5: Test Your Deployment**

1. **Test Frontend**
   - Visit: https://nextlevelsbs.com/sop/
   - Should see your SOP Builder interface

2. **Test API Connection**
   - In browser console, check for API errors
   - Visit: https://your-api-url.com/api/health
   - Should return JSON with status "healthy"

3. **Test Full Functionality**
   - Try browsing templates
   - Test SOP generation
   - Verify all features work

## 🔧 **Troubleshooting**

### **Frontend Issues**
- **404 Error**: Check file paths in Hostinger
- **Blank Page**: Check browser console for errors
- **API Errors**: Verify API URL in environment file

### **Backend Issues**
- **Server Won't Start**: Check Python dependencies
- **API Timeout**: Verify server is running and accessible
- **CORS Errors**: Check allowed origins in API configuration

### **Common Fixes**
```bash
# Rebuild with correct API URL
cd sop-wizard-pro-main
echo "VITE_API_BASE_URL=https://your-actual-api-url.com" > .env.production
npm run build

# Test API locally
curl https://your-api-url.com/api/health

# Check API logs (Railway/Render dashboard)
```

## 💰 **Cost Summary**

### **Recommended Setup (Railway + Hostinger)**
- **Hostinger**: $0 (existing)
- **Railway**: $0 (free tier)
- **Total**: $0/month

### **Professional Setup (DigitalOcean + Hostinger)**
- **Hostinger**: $0 (existing)
- **DigitalOcean**: $6/month
- **Total**: $6/month

## 🎉 **Final URLs**

After successful deployment:

- **Main App**: https://nextlevelsbs.com/sop/
- **Templates**: https://nextlevelsbs.com/sop/templates
- **Advanced Generator**: https://nextlevelsbs.com/sop/generate-advanced/restaurant-opening
- **API Health**: https://your-api-url.com/api/health

## 📞 **Need Help?**

If you encounter any issues:

1. **Check the browser console** for JavaScript errors
2. **Verify API connectivity** by visiting the health endpoint
3. **Review deployment logs** in Railway/Render dashboard
4. **Test locally first** to ensure everything works

**Your SOP Builder MVP will be live at nextlevelsbs.com/sop/ once deployed!** 🚀
