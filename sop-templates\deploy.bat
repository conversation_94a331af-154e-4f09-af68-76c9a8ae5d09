@echo off
REM SOP Builder MVP - Production Deployment Script (Windows)
REM Builds frontend and prepares for deployment

echo 🚀 Starting SOP Builder MVP deployment preparation...

REM Check if we're in the right directory
if not exist "deploy.bat" (
    echo [ERROR] Please run this script from the sop-templates directory
    pause
    exit /b 1
)

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo [INFO] Building React frontend for production...

REM Navigate to frontend directory
cd sop-wizard-pro-main

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo [INFO] Installing npm dependencies...
    npm install
)

REM Build for production
echo [INFO] Building production bundle...
npm run build

REM Check if build was successful
if not exist "dist" (
    echo [ERROR] Build failed - dist directory not created
    pause
    exit /b 1
)

echo [SUCCESS] Frontend build completed successfully!

REM Go back to root directory
cd ..

REM Create deployment directory with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%-%HH%%Min%%Sec%"

set "DEPLOY_DIR=deployment-%timestamp%"

echo [INFO] Creating deployment package...
mkdir "%DEPLOY_DIR%"

REM Copy frontend build
echo [INFO] Copying frontend files...
xcopy /E /I "sop-wizard-pro-main\dist" "%DEPLOY_DIR%\frontend"

REM Copy backend files
echo [INFO] Copying backend files...
mkdir "%DEPLOY_DIR%\backend"
xcopy /E /I "api" "%DEPLOY_DIR%\backend\api"
xcopy /E /I "scripts" "%DEPLOY_DIR%\backend\scripts"

REM Copy configuration files
copy "requirements.txt" "%DEPLOY_DIR%\backend\" >nul 2>nul

REM Create deployment instructions
echo # SOP Builder MVP - Deployment Package > "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo ## Frontend Deployment (Hostinger) >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 1. **Upload frontend files to Hostinger:** >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo    - Upload contents of `frontend/` folder to `public_html/sop/` >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo    - Ensure `index.html` is in the root of the sop directory >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 2. **Access URL:** >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo    - https://nextlevelsbs.com/sop/ >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo ## Backend Deployment Options >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo ### Option A: Railway (Free Tier) >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 1. Connect your GitHub repository to Railway >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 2. Deploy the backend folder >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 3. Set environment variables in Railway dashboard >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo ### Option B: DigitalOcean Droplet >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 1. Create Ubuntu 22.04 droplet ($6/month) >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 2. Upload backend files to server >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 3. Install Python dependencies: `pip install -r requirements.txt` >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"
echo 4. Run API server: `python api/simple_server.py` >> "%DEPLOY_DIR%\DEPLOYMENT_INSTRUCTIONS.md"

REM Create startup script for backend
echo @echo off > "%DEPLOY_DIR%\backend\start_server.bat"
echo REM SOP Builder MVP - Server Startup Script >> "%DEPLOY_DIR%\backend\start_server.bat"
echo. >> "%DEPLOY_DIR%\backend\start_server.bat"
echo echo 🚀 Starting SOP Builder MVP API Server... >> "%DEPLOY_DIR%\backend\start_server.bat"
echo. >> "%DEPLOY_DIR%\backend\start_server.bat"
echo REM Install dependencies >> "%DEPLOY_DIR%\backend\start_server.bat"
echo echo 📦 Installing dependencies... >> "%DEPLOY_DIR%\backend\start_server.bat"
echo pip install -r requirements.txt >> "%DEPLOY_DIR%\backend\start_server.bat"
echo. >> "%DEPLOY_DIR%\backend\start_server.bat"
echo REM Start the server >> "%DEPLOY_DIR%\backend\start_server.bat"
echo echo 🌐 Starting API server on port 8000... >> "%DEPLOY_DIR%\backend\start_server.bat"
echo python api/simple_server.py >> "%DEPLOY_DIR%\backend\start_server.bat"

echo [SUCCESS] Deployment package created successfully!
echo.
echo 📦 Deployment files ready:
echo    📁 Directory: %DEPLOY_DIR%\
echo.
echo 🌐 Next steps:
echo    1. Upload frontend\ contents to Hostinger: public_html/sop/
echo    2. Deploy backend\ to your chosen platform
echo    3. Update API URL if needed
echo    4. Test at: https://nextlevelsbs.com/sop/
echo.
echo [SUCCESS] Ready for production deployment! 🚀
echo.
echo Press any key to continue...
pause >nul
