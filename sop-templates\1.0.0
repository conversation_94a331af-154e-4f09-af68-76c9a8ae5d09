Requirement already satisfied: openai in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (1.82.0)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (0.10.0)
Requirement already satisfied: pydantic<3,>=1.9.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (2.11.5)
Requirement already satisfied: sniffio in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (1.3.1)
Requirement already satisfied: tqdm>4 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from openai) (4.13.2)
Requirement already satisfied: idna>=2.8 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from httpx<1,>=0.23.0->openai) (2025.4.26)
Requirement already satisfied: httpcore==1.* in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from httpx<1,>=0.23.0->openai) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from pydantic<3,>=1.9.0->openai) (0.4.1)
Requirement already satisfied: colorama in c:\projects\sop-builder-mvp\sop-templates\venv\lib\site-packages (from tqdm>4->openai) (0.4.6)
