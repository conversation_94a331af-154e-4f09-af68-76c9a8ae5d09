
import { useState } from "react";
import { use<PERSON>ara<PERSON>, <PERSON> } from "react-router-dom";
import { ArrowLeft, Download, Share2, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Layout from "@/components/Layout";

const Preview = () => {
  const { documentId } = useParams();
  const [zoomLevel, setZoomLevel] = useState(100);

  // Mock document data
  const document = {
    id: documentId,
    title: "Restaurant Opening Procedures - Bella Vista",
    templateType: "Restaurant Operations",
    createdDate: "2025-01-29",
    provider: "Groq",
    model: "llama-3.1-70b-versatile",
    fileSize: "2.3 MB",
    pages: 12,
    sections: [
      "Introduction & Purpose",
      "Daily Opening Checklist", 
      "Equipment Safety Checks",
      "Food Safety Protocols",
      "HACCP Procedures",
      "Staff Preparation",
      "Cleaning & Sanitization",
      "Inventory Management",
      "Emergency Procedures",
      "Compliance Documentation",
      "Quality Control",
      "Appendices"
    ],
    generationStats: {
      totalSections: 6,
      successfulSections: 6,
      generationTimeSeconds: 23.45,
      providerUsed: "groq",
      modelUsed: "llama-3.1-70b-versatile"
    },
    compliance: ["FDA Food Code", "HACCP", "ServSafe"],
    icon: "🍽️"
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 50));
  };

  const handleResetZoom = () => {
    setZoomLevel(100);
  };

  const getProviderColor = (provider: string) => {
    const colors = {
      "Groq": "bg-purple-100 text-purple-800",
      "Hugging Face": "bg-yellow-100 text-yellow-800",
      "Together AI": "bg-green-100 text-green-800", 
      "OpenRouter": "bg-blue-100 text-blue-800"
    };
    return colors[provider as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link to="/documents">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Documents
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-[#2C3E50]">{document.title}</h1>
              <p className="text-gray-600">{document.templateType}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button className="bg-[#3498DB] hover:bg-[#2980B9]">
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Document Info Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <div className="text-3xl mb-2">{document.icon}</div>
                <CardTitle className="text-lg">Document Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Created:</span>
                    <span className="font-medium">{new Date(document.createdDate).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Pages:</span>
                    <span className="font-medium">{document.pages}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">File Size:</span>
                    <span className="font-medium">{document.fileSize}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Provider:</span>
                    <Badge className={getProviderColor(document.provider)}>
                      {document.provider}
                    </Badge>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Compliance Standards</h4>
                  <div className="flex flex-wrap gap-1">
                    {document.compliance.map((standard) => (
                      <Badge key={standard} variant="secondary" className="text-xs">
                        {standard}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Table of Contents</h4>
                  <div className="space-y-1 max-h-64 overflow-y-auto">
                    {document.sections.map((section, index) => (
                      <div key={index} className="text-sm text-gray-600 hover:text-[#3498DB] cursor-pointer py-1">
                        {index + 1}. {section}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Generation Stats</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>Sections: {document.generationStats.successfulSections}/{document.generationStats.totalSections}</div>
                    <div>Time: {document.generationStats.generationTimeSeconds}s</div>
                    <div>Model: {document.generationStats.modelUsed}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* PDF Preview */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Document Preview</CardTitle>
                    <CardDescription>
                      Interactive PDF preview with navigation controls
                    </CardDescription>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={handleZoomOut}>
                      <ZoomOut className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleResetZoom}>
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleZoomIn}>
                      <ZoomIn className="w-4 h-4" />
                    </Button>
                    <span className="text-sm text-gray-600 ml-2">{zoomLevel}%</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div 
                  className="border rounded-lg bg-white shadow-inner min-h-[600px] flex items-center justify-center"
                  style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top left' }}
                >
                  {/* Mock PDF Preview */}
                  <div className="bg-white shadow-lg max-w-2xl w-full min-h-[800px] p-8 border">
                    <div className="text-center mb-8">
                      <h1 className="text-3xl font-bold text-[#2C3E50] mb-2">
                        Restaurant Opening Procedures
                      </h1>
                      <h2 className="text-xl text-gray-600 mb-4">Bella Vista Restaurant</h2>
                      <div className="w-16 h-16 bg-[#2C3E50] rounded-lg mx-auto mb-4 flex items-center justify-center text-2xl">
                        🍽️
                      </div>
                      <p className="text-gray-500">Standard Operating Procedures</p>
                      <p className="text-sm text-gray-400 mt-2">Generated on {new Date(document.createdDate).toLocaleDateString()}</p>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-bold text-[#2C3E50] mb-3 border-b pb-2">
                          1. Introduction & Purpose
                        </h3>
                        <p className="text-gray-700 leading-relaxed">
                          This Standard Operating Procedure (SOP) provides comprehensive guidelines for the daily opening procedures at Bella Vista Restaurant. These procedures ensure compliance with FDA Food Code, HACCP requirements, and ServSafe standards while maintaining the highest levels of food safety and operational efficiency.
                        </p>
                      </div>

                      <div>
                        <h3 className="text-lg font-bold text-[#2C3E50] mb-3 border-b pb-2">
                          2. Daily Opening Checklist
                        </h3>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 border border-gray-400 rounded"></div>
                            <span className="text-gray-700">Verify all refrigeration units are at proper temperatures (≤41°F)</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 border border-gray-400 rounded"></div>
                            <span className="text-gray-700">Check hot holding equipment reaches 165°F minimum</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 border border-gray-400 rounded"></div>
                            <span className="text-gray-700">Inspect all food items for signs of spoilage or contamination</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 border border-gray-400 rounded"></div>
                            <span className="text-gray-700">Verify proper hand washing station setup and supplies</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-bold text-[#2C3E50] mb-3 border-b pb-2">
                          3. Equipment Safety Checks
                        </h3>
                        <p className="text-gray-700 leading-relaxed">
                          All kitchen equipment must be inspected for proper operation and safety compliance before beginning food preparation activities...
                        </p>
                      </div>
                    </div>

                    <div className="mt-8 pt-4 border-t text-center">
                      <p className="text-xs text-gray-400">
                        Page 1 of {document.pages} | Generated by Premium SOP Templates
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Preview;
