# PDF Generation Optimization Plan

## 1. 🌐 Web-based PDF Preview Implementation

### Real-time Preview Architecture
```python
# Backend: Fast preview generation service
class PDFPreviewService:
    def __init__(self):
        self.preview_cache = {}
        self.preview_quality = 'medium'  # Faster generation for previews
        
    async def generate_preview(self, template_data: dict, brand_config: dict) -> str:
        """Generate low-resolution PDF preview for web display"""
        
        # Create cache key from content hash
        cache_key = self.generate_cache_key(template_data, brand_config)
        
        if cache_key in self.preview_cache:
            return self.preview_cache[cache_key]
        
        # Generate preview with optimized settings
        preview_pdf = await self.create_preview_pdf(template_data, brand_config)
        
        # Convert to base64 for web display
        preview_base64 = self.pdf_to_base64(preview_pdf)
        
        # Cache for 30 minutes
        self.preview_cache[cache_key] = preview_base64
        asyncio.create_task(self.expire_cache_entry(cache_key, 1800))
        
        return preview_base64
    
    async def create_preview_pdf(self, template_data: dict, brand_config: dict):
        """Create optimized preview PDF"""
        # Use existing PDF generator with preview optimizations
        generator = EnhancedSOPPDFGenerator(brand_config)
        
        # Preview-specific optimizations
        generator.set_preview_mode(
            dpi=72,  # Lower DPI for faster generation
            image_quality=60,  # Compressed images
            font_embedding=False  # Skip font embedding for speed
        )
        
        return generator.generate_enhanced_pdf(template_data, preview=True)
```

### Frontend Preview Component
```typescript
// Frontend: Real-time PDF preview with optimization
interface PDFPreviewProps {
  templateData: any;
  brandConfig: BrandConfig;
  onPreviewReady?: (previewUrl: string) => void;
}

const PDFPreview: React.FC<PDFPreviewProps> = ({ 
  templateData, 
  brandConfig, 
  onPreviewReady 
}) => {
  const [previewUrl, setPreviewUrl] = useState<string>();
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string>();
  
  // Debounced preview generation
  const debouncedGenerate = useCallback(
    debounce(async (data: any, config: BrandConfig) => {
      setIsGenerating(true);
      setError(undefined);
      
      try {
        const response = await api.post('/documents/preview', {
          template_data: data,
          brand_config: config,
          preview_options: {
            quality: 'medium',
            include_watermark: true
          }
        });
        
        const previewUrl = `data:application/pdf;base64,${response.data.preview_base64}`;
        setPreviewUrl(previewUrl);
        onPreviewReady?.(previewUrl);
        
      } catch (err) {
        setError('Failed to generate preview');
        console.error('Preview generation error:', err);
      } finally {
        setIsGenerating(false);
      }
    }, 1000),
    []
  );
  
  useEffect(() => {
    if (templateData && brandConfig) {
      debouncedGenerate(templateData, brandConfig);
    }
  }, [templateData, brandConfig, debouncedGenerate]);
  
  return (
    <div className="pdf-preview-container">
      <div className="preview-header">
        <h3>PDF Preview</h3>
        {isGenerating && (
          <div className="preview-status">
            <Spinner size="sm" />
            <span>Generating preview...</span>
          </div>
        )}
      </div>
      
      <div className="preview-content">
        {error ? (
          <div className="preview-error">
            <AlertCircle className="error-icon" />
            <p>{error}</p>
            <Button onClick={() => debouncedGenerate(templateData, brandConfig)}>
              Retry
            </Button>
          </div>
        ) : previewUrl ? (
          <iframe
            src={previewUrl}
            className="pdf-preview-frame"
            title="PDF Preview"
            loading="lazy"
          />
        ) : (
          <div className="preview-placeholder">
            <FileText className="placeholder-icon" />
            <p>Preview will appear here</p>
          </div>
        )}
      </div>
    </div>
  );
};
```

## 2. 🤖 Real-time SOP Generation with LLM Integration

### Streaming Generation with Progress Updates
```python
# Backend: Streaming SOP generation
class StreamingSOPGenerator:
    def __init__(self):
        self.llm_client = FreeLLMClient()
        self.websocket_manager = WebSocketManager()
    
    async def generate_sop_streaming(self, 
                                   generation_id: str, 
                                   template_request: dict,
                                   websocket_connection=None):
        """Generate SOP with real-time progress updates"""
        
        try:
            # Step 1: Initialize generation
            await self.send_progress(generation_id, {
                "step": "initializing",
                "progress": 0,
                "message": "Initializing AI provider..."
            })
            
            # Step 2: Generate content sections
            sections = await self.generate_sections_streaming(
                template_request, generation_id
            )
            
            # Step 3: Apply compliance requirements
            await self.send_progress(generation_id, {
                "step": "compliance",
                "progress": 70,
                "message": "Adding compliance requirements..."
            })
            
            compliant_sections = await self.add_compliance_streaming(
                sections, template_request['industry']
            )
            
            # Step 4: Generate PDF
            await self.send_progress(generation_id, {
                "step": "pdf_generation",
                "progress": 85,
                "message": "Generating PDF document..."
            })
            
            pdf_result = await self.generate_pdf_async(
                compliant_sections, template_request['brand_config']
            )
            
            # Step 5: Complete
            await self.send_progress(generation_id, {
                "step": "completed",
                "progress": 100,
                "message": "Generation complete!",
                "result": {
                    "document_id": pdf_result['document_id'],
                    "download_url": pdf_result['download_url'],
                    "preview_url": pdf_result['preview_url']
                }
            })
            
        except Exception as e:
            await self.send_progress(generation_id, {
                "step": "error",
                "progress": 0,
                "message": f"Generation failed: {str(e)}",
                "error": str(e)
            })
            raise
    
    async def generate_sections_streaming(self, request: dict, generation_id: str):
        """Generate SOP sections with progress updates"""
        sections = []
        section_types = ['introduction', 'procedures', 'safety', 'compliance']
        
        for i, section_type in enumerate(section_types):
            progress = 10 + (i * 15)  # 10-55% for content generation
            
            await self.send_progress(generation_id, {
                "step": "content_generation",
                "progress": progress,
                "message": f"Generating {section_type} section..."
            })
            
            # Use existing LLM client with progress callback
            section_content = await self.llm_client.generate_async(
                system_prompt=self.get_section_prompt(section_type, request),
                user_prompt=self.get_user_prompt(section_type, request),
                progress_callback=lambda p: self.send_section_progress(
                    generation_id, section_type, p
                )
            )
            
            sections.append({
                'type': section_type,
                'content': section_content.content,
                'provider': section_content.provider
            })
        
        return sections
```

### Frontend Real-time Updates
```typescript
// Frontend: Real-time generation tracking
const useSOPGeneration = () => {
  const [generationState, setGenerationState] = useState<GenerationState>({
    status: 'idle',
    progress: 0,
    currentStep: '',
    result: null,
    error: null
  });
  
  const startGeneration = useCallback(async (request: GenerationRequest) => {
    try {
      // Start generation
      const response = await api.post('/generate', request);
      const { generation_id, websocket_url } = response.data;
      
      // Connect to WebSocket for real-time updates
      const ws = new WebSocket(websocket_url);
      
      ws.onmessage = (event) => {
        const update = JSON.parse(event.data);
        
        setGenerationState(prev => ({
          ...prev,
          status: update.step === 'completed' ? 'completed' : 'generating',
          progress: update.progress,
          currentStep: update.message,
          result: update.result || prev.result,
          error: update.error || null
        }));
        
        // Close WebSocket when complete
        if (update.step === 'completed' || update.step === 'error') {
          ws.close();
        }
      };
      
      ws.onerror = () => {
        setGenerationState(prev => ({
          ...prev,
          status: 'error',
          error: 'Connection lost during generation'
        }));
      };
      
    } catch (error) {
      setGenerationState(prev => ({
        ...prev,
        status: 'error',
        error: 'Failed to start generation'
      }));
    }
  }, []);
  
  return { generationState, startGeneration };
};
```

## 3. 📦 Batch Processing Capabilities

### Multi-document Generation
```python
# Backend: Batch processing service
class BatchProcessingService:
    def __init__(self):
        self.max_concurrent = 3  # Limit concurrent generations
        self.queue = asyncio.Queue()
        self.active_jobs = {}
    
    async def process_batch_request(self, batch_request: dict) -> str:
        """Process multiple SOP generations in batch"""
        batch_id = str(uuid.uuid4())
        
        # Create batch job
        batch_job = {
            'id': batch_id,
            'total_items': len(batch_request['items']),
            'completed_items': 0,
            'failed_items': 0,
            'status': 'processing',
            'results': [],
            'created_at': datetime.utcnow()
        }
        
        self.active_jobs[batch_id] = batch_job
        
        # Process items with concurrency limit
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_item(item):
            async with semaphore:
                try:
                    result = await self.generate_single_sop(item)
                    batch_job['results'].append({
                        'item_id': item['id'],
                        'status': 'completed',
                        'result': result
                    })
                    batch_job['completed_items'] += 1
                    
                except Exception as e:
                    batch_job['results'].append({
                        'item_id': item['id'],
                        'status': 'failed',
                        'error': str(e)
                    })
                    batch_job['failed_items'] += 1
                
                # Send progress update
                await self.send_batch_progress(batch_id, batch_job)
        
        # Start all tasks
        tasks = [process_item(item) for item in batch_request['items']]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Mark batch as complete
        batch_job['status'] = 'completed'
        await self.send_batch_progress(batch_id, batch_job)
        
        return batch_id
    
    async def get_batch_status(self, batch_id: str) -> dict:
        """Get current status of batch job"""
        return self.active_jobs.get(batch_id, {'status': 'not_found'})
```

### Frontend Batch Interface
```typescript
// Frontend: Batch generation interface
const BatchGenerator = () => {
  const [batchItems, setBatchItems] = useState<BatchItem[]>([]);
  const [batchStatus, setBatchStatus] = useState<BatchStatus>();
  
  const addBatchItem = (template: Template, customization: any) => {
    const newItem: BatchItem = {
      id: generateId(),
      template_id: template.id,
      customization,
      status: 'pending'
    };
    setBatchItems(prev => [...prev, newItem]);
  };
  
  const startBatchGeneration = async () => {
    try {
      const response = await api.post('/generate/batch', {
        items: batchItems.map(item => ({
          id: item.id,
          template_id: item.template_id,
          customization: item.customization
        }))
      });
      
      const { batch_id, websocket_url } = response.data;
      
      // Connect to batch progress WebSocket
      const ws = new WebSocket(websocket_url);
      ws.onmessage = (event) => {
        const update = JSON.parse(event.data);
        setBatchStatus(update);
        
        // Update individual item statuses
        setBatchItems(prev => prev.map(item => {
          const result = update.results.find(r => r.item_id === item.id);
          return result ? { ...item, status: result.status } : item;
        }));
      };
      
    } catch (error) {
      console.error('Batch generation failed:', error);
    }
  };
  
  return (
    <div className="batch-generator">
      <div className="batch-queue">
        <h3>Generation Queue ({batchItems.length} items)</h3>
        {batchItems.map(item => (
          <BatchItemCard key={item.id} item={item} />
        ))}
      </div>
      
      {batchStatus && (
        <div className="batch-progress">
          <h4>Batch Progress</h4>
          <Progress 
            value={(batchStatus.completed_items / batchStatus.total_items) * 100} 
          />
          <p>
            {batchStatus.completed_items} of {batchStatus.total_items} completed
            {batchStatus.failed_items > 0 && ` (${batchStatus.failed_items} failed)`}
          </p>
        </div>
      )}
      
      <Button 
        onClick={startBatchGeneration}
        disabled={batchItems.length === 0 || batchStatus?.status === 'processing'}
      >
        Generate All SOPs
      </Button>
    </div>
  );
};
```

## 4. ⚡ Performance Optimization for Commercial Scale

### Caching Strategy
```python
# Backend: Multi-level caching
class CachingStrategy:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.memory_cache = {}
        self.file_cache_dir = Path("cache/generated")
    
    async def get_cached_generation(self, request_hash: str):
        """Check all cache levels for existing generation"""
        
        # Level 1: Memory cache (fastest)
        if request_hash in self.memory_cache:
            return self.memory_cache[request_hash]
        
        # Level 2: Redis cache (fast)
        cached_data = self.redis_client.get(f"generation:{request_hash}")
        if cached_data:
            result = json.loads(cached_data)
            self.memory_cache[request_hash] = result  # Promote to memory
            return result
        
        # Level 3: File cache (persistent)
        cache_file = self.file_cache_dir / f"{request_hash}.json"
        if cache_file.exists():
            with open(cache_file) as f:
                result = json.load(f)
            
            # Promote to higher cache levels
            self.redis_client.setex(f"generation:{request_hash}", 3600, json.dumps(result))
            self.memory_cache[request_hash] = result
            return result
        
        return None
    
    async def cache_generation(self, request_hash: str, result: dict):
        """Store generation result in all cache levels"""
        
        # Memory cache
        self.memory_cache[request_hash] = result
        
        # Redis cache (1 hour)
        self.redis_client.setex(f"generation:{request_hash}", 3600, json.dumps(result))
        
        # File cache (persistent)
        cache_file = self.file_cache_dir / f"{request_hash}.json"
        cache_file.parent.mkdir(parents=True, exist_ok=True)
        with open(cache_file, 'w') as f:
            json.dump(result, f)
```

### Database Optimization
```python
# Backend: Optimized database queries
class OptimizedQueries:
    def __init__(self, db_session):
        self.db = db_session
    
    async def get_user_documents_paginated(self, 
                                         user_id: int, 
                                         page: int = 1, 
                                         per_page: int = 20):
        """Optimized document listing with pagination"""
        
        # Use database-level pagination
        offset = (page - 1) * per_page
        
        query = (
            select(Document)
            .where(Document.user_id == user_id)
            .order_by(Document.created_at.desc())
            .offset(offset)
            .limit(per_page)
        )
        
        # Execute with eager loading for related data
        result = await self.db.execute(
            query.options(selectinload(Document.template))
        )
        
        documents = result.scalars().all()
        
        # Get total count for pagination
        count_query = (
            select(func.count(Document.id))
            .where(Document.user_id == user_id)
        )
        total_count = await self.db.scalar(count_query)
        
        return {
            'documents': documents,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': math.ceil(total_count / per_page)
            }
        }
```

### Resource Management
```python
# Backend: Resource monitoring and management
class ResourceManager:
    def __init__(self):
        self.max_concurrent_generations = 10
        self.max_memory_usage = 0.8  # 80% of available memory
        self.generation_semaphore = asyncio.Semaphore(self.max_concurrent_generations)
    
    async def check_resources(self):
        """Check system resources before starting generation"""
        import psutil
        
        # Check memory usage
        memory = psutil.virtual_memory()
        if memory.percent > (self.max_memory_usage * 100):
            raise ResourceError("Insufficient memory available")
        
        # Check disk space
        disk = psutil.disk_usage('/')
        if disk.free < (1024 * 1024 * 1024):  # 1GB minimum
            raise ResourceError("Insufficient disk space")
        
        return True
    
    async def managed_generation(self, generation_func, *args, **kwargs):
        """Execute generation with resource management"""
        async with self.generation_semaphore:
            await self.check_resources()
            return await generation_func(*args, **kwargs)
```

## 5. 📊 Analytics and Monitoring

### Generation Analytics
```python
# Backend: Analytics tracking
class GenerationAnalytics:
    def __init__(self):
        self.metrics = {}
    
    async def track_generation(self, 
                             template_id: str, 
                             user_id: int, 
                             generation_time: float,
                             llm_provider: str,
                             success: bool):
        """Track generation metrics"""
        
        # Store in database
        analytics_record = TemplateUsage(
            template_id=template_id,
            user_id=user_id,
            generation_time_seconds=generation_time,
            llm_provider=llm_provider,
            success=success,
            created_at=datetime.utcnow()
        )
        
        await self.db.add(analytics_record)
        await self.db.commit()
        
        # Update real-time metrics
        self.update_realtime_metrics(template_id, generation_time, success)
    
    def get_performance_metrics(self) -> dict:
        """Get current performance metrics"""
        return {
            'average_generation_time': self.calculate_avg_generation_time(),
            'success_rate': self.calculate_success_rate(),
            'popular_templates': self.get_popular_templates(),
            'llm_provider_performance': self.get_provider_performance()
        }
```
