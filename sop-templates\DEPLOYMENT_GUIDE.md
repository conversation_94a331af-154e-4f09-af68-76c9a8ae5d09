# 🚀 SOP Builder MVP - Production Deployment Guide

## 📋 Deployment Overview

**Target Domain**: nextlevelsbs.com  
**Current Host**: Hostinger.com  
**Architecture**: React Frontend + Python Backend  
**Cost**: Minimal (using existing hosting)

## 🌐 Option 1: Hostinger Shared Hosting (Recommended)

### **Step 1: Build React Frontend for Production**

```bash
# Navigate to frontend directory
cd sop-templates/sop-wizard-pro-main

# Install dependencies (if not already done)
npm install

# Build for production
npm run build
```

This creates a `dist/` folder with optimized static files.

### **Step 2: Upload Frontend to Hostinger**

1. **Access Hostinger File Manager** or use FTP
2. **Navigate to** `public_html/` (or your domain's root)
3. **Create SOP subdirectory**: `public_html/sop/`
4. **Upload contents** of `dist/` folder to `public_html/sop/`

**File structure on server:**
```
public_html/
├── sop/
│   ├── index.html
│   ├── assets/
│   │   ├── index-[hash].js
│   │   ├── index-[hash].css
│   │   └── ...
│   └── ...
└── (your existing website files)
```

### **Step 3: Backend Deployment Options**

#### **Option A: External VPS (Recommended)**
- **Cost**: $5-10/month
- **Providers**: DigitalOcean, Linode, Vultr
- **Benefits**: Full Python support, API hosting

#### **Option B: Hostinger VPS**
- **Cost**: $3.99-8.99/month
- **Benefits**: Same provider, easy management

#### **Option C: Free Tier Services**
- **Railway**: Free tier with Python support
- **Render**: Free tier for APIs
- **Heroku**: Limited free options

## 🔧 Option 2: Full VPS Deployment (Professional)

### **Recommended VPS Providers**

1. **DigitalOcean Droplet**
   - **Cost**: $6/month (1GB RAM)
   - **Setup**: Ubuntu 22.04 LTS
   - **Benefits**: Excellent documentation

2. **Linode Nanode**
   - **Cost**: $5/month (1GB RAM)
   - **Benefits**: Great performance

3. **Vultr Regular Performance**
   - **Cost**: $6/month (1GB RAM)
   - **Benefits**: Global locations

### **VPS Setup Process**

#### **1. Server Preparation**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3 python3-pip python3-venv nginx certbot python3-certbot-nginx nodejs npm

# Create application user
sudo useradd -m -s /bin/bash sopbuilder
sudo usermod -aG sudo sopbuilder
```

#### **2. Application Deployment**
```bash
# Switch to app user
sudo su - sopbuilder

# Clone your repository
git clone https://github.com/yourusername/SOP-Builder-MVP.git
cd SOP-Builder-MVP/sop-templates

# Set up Python environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Build frontend
cd sop-wizard-pro-main
npm install
npm run build
cd ..
```

#### **3. Nginx Configuration**
```nginx
# /etc/nginx/sites-available/nextlevelsbs.com
server {
    listen 80;
    server_name nextlevelsbs.com www.nextlevelsbs.com;

    # SOP Builder Frontend
    location /sop/ {
        alias /home/<USER>/SOP-Builder-MVP/sop-templates/sop-wizard-pro-main/dist/;
        try_files $uri $uri/ /sop/index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API Backend
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Your existing website
    location / {
        root /var/www/nextlevelsbs.com;
        index index.html index.php;
        try_files $uri $uri/ =404;
    }
}
```

#### **4. SSL Certificate**
```bash
# Install SSL certificate
sudo certbot --nginx -d nextlevelsbs.com -d www.nextlevelsbs.com
```

#### **5. Process Management**
```bash
# Create systemd service for API
sudo nano /etc/systemd/system/sopbuilder-api.service
```

```ini
[Unit]
Description=SOP Builder MVP API
After=network.target

[Service]
Type=simple
User=sopbuilder
WorkingDirectory=/home/<USER>/SOP-Builder-MVP/sop-templates
Environment=PATH=/home/<USER>/SOP-Builder-MVP/sop-templates/venv/bin
ExecStart=/home/<USER>/SOP-Builder-MVP/sop-templates/venv/bin/python api/simple_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable sopbuilder-api
sudo systemctl start sopbuilder-api
sudo systemctl status sopbuilder-api
```

## 🔒 Production Configuration

### **Environment Variables**
```bash
# Create production environment file
nano /home/<USER>/SOP-Builder-MVP/sop-templates/.env.production

# Add configuration
ENVIRONMENT=production
API_BASE_URL=https://nextlevelsbs.com
OPENROUTER_API_KEY=your_openrouter_key_here
ALLOWED_ORIGINS=https://nextlevelsbs.com,https://www.nextlevelsbs.com
```

### **Frontend Build Configuration**
```bash
# Update Vite config for production
cd sop-wizard-pro-main
nano .env.production

# Add production API URL
VITE_API_BASE_URL=https://nextlevelsbs.com
```

### **Security Hardening**
```bash
# Firewall setup
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Secure SSH (optional)
sudo nano /etc/ssh/sshd_config
# Set: PermitRootLogin no
# Set: PasswordAuthentication no (if using SSH keys)
sudo systemctl restart ssh
```

## 💰 Cost Breakdown

### **Option 1: Hostinger + External API**
- **Hostinger**: $0 (existing)
- **VPS for API**: $5-10/month
- **Total**: $5-10/month

### **Option 2: Full VPS**
- **VPS**: $5-6/month
- **Domain**: $0 (existing)
- **Total**: $5-6/month

### **Option 3: Hybrid (Recommended)**
- **Hostinger**: $0 (frontend)
- **Railway/Render**: $0-5/month (API)
- **Total**: $0-5/month

## 🚀 Quick Start Deployment

### **Immediate Steps (Today)**

1. **Build Frontend**:
   ```bash
   cd sop-templates/sop-wizard-pro-main
   npm run build
   ```

2. **Upload to Hostinger**:
   - Upload `dist/` contents to `public_html/sop/`

3. **Deploy API** (Choose one):
   - **Railway**: Connect GitHub repo, auto-deploy
   - **Render**: Connect GitHub repo, auto-deploy
   - **DigitalOcean**: Manual VPS setup

4. **Update DNS** (if needed):
   - Point subdomain to VPS IP
   - Or use existing domain with path routing

### **Testing URLs**
- **Frontend**: https://nextlevelsbs.com/sop/
- **API**: https://your-api-domain.com/api/health
- **Full App**: https://nextlevelsbs.com/sop/templates

## 📞 Next Steps

1. **Choose deployment option** based on budget/complexity
2. **Set up production environment**
3. **Configure domain/subdomain**
4. **Deploy and test**
5. **Set up monitoring and backups**

**Recommended**: Start with Option 1 (Hostinger + Railway) for fastest, cheapest deployment!
