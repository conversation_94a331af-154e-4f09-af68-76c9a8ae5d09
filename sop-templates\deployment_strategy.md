# Deployment Strategy

## 1. 🏠 Local Development Environment Setup

### Prerequisites
- Python 3.9+
- Node.js 18+
- SQLite (development)
- Redis (for background tasks)

### Step-by-Step Setup

#### Backend Setup
```bash
# 1. Install Python dependencies
cd sop-templates
pip install -r requirements.txt

# 2. Install additional FastAPI dependencies
pip install fastapi uvicorn sqlalchemy alembic redis celery python-jose[cryptography] python-multipart

# 3. Set up environment variables
cp .env.example .env.local
# Edit .env.local with development settings

# 4. Initialize database
alembic init alembic
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head

# 5. Start Redis (for background tasks)
redis-server

# 6. Start Celery worker (in separate terminal)
celery -A api.background_tasks worker --loglevel=info

# 7. Start FastAPI development server
uvicorn api.main:app --reload --port 8000
```

#### Frontend Setup
```bash
# 1. Navigate to frontend directory
cd sop-wizard-pro-main

# 2. Install dependencies
npm install

# 3. Configure environment
cp .env.example .env.local
# Edit .env.local:
# VITE_API_BASE_URL=http://localhost:8000/api/v1
# VITE_WS_BASE_URL=ws://localhost:8000/ws

# 4. Start development server
npm run dev
```

### Development Workflow
```bash
# Terminal 1: Backend API
uvicorn api.main:app --reload --port 8000

# Terminal 2: Frontend
cd sop-wizard-pro-main && npm run dev

# Terminal 3: Background tasks
celery -A api.background_tasks worker --loglevel=info

# Terminal 4: Redis
redis-server
```

## 2. 🌐 Production Deployment Strategy

### Architecture Overview
```
Internet → Cloudflare → Nginx → FastAPI + React (nextlevelsbs.com)
                              ↓
                         PostgreSQL + Redis
```

### Infrastructure Requirements

#### Server Specifications
- **CPU**: 4 cores minimum (8 cores recommended)
- **RAM**: 8GB minimum (16GB recommended)
- **Storage**: 100GB SSD minimum
- **Bandwidth**: 1TB/month minimum

#### Technology Stack
- **Web Server**: Nginx (reverse proxy + static files)
- **API Server**: FastAPI with Gunicorn
- **Database**: PostgreSQL 14+
- **Cache/Queue**: Redis 6+
- **Process Manager**: Supervisor
- **SSL**: Let's Encrypt via Certbot

### Step-by-Step Production Deployment

#### 1. Server Setup (Ubuntu 22.04 LTS)
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3.9 python3.9-venv python3-pip nodejs npm nginx postgresql postgresql-contrib redis-server supervisor certbot python3-certbot-nginx

# Create application user
sudo useradd -m -s /bin/bash sopbuilder
sudo usermod -aG sudo sopbuilder
```

#### 2. Database Setup
```bash
# Configure PostgreSQL
sudo -u postgres createuser --interactive sopbuilder
sudo -u postgres createdb sopbuilder_prod

# Set up database user
sudo -u postgres psql
ALTER USER sopbuilder WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE sopbuilder_prod TO sopbuilder;
\q
```

#### 3. Application Deployment
```bash
# Clone repository
sudo -u sopbuilder git clone https://github.com/trevden810/SOP-Builder-MVP.git /home/<USER>/app
cd /home/<USER>/app/sop-templates

# Set up Python environment
sudo -u sopbuilder python3.9 -m venv venv
sudo -u sopbuilder ./venv/bin/pip install -r requirements.txt
sudo -u sopbuilder ./venv/bin/pip install gunicorn psycopg2-binary

# Build frontend
cd sop-wizard-pro-main
sudo -u sopbuilder npm install
sudo -u sopbuilder npm run build

# Copy built files to nginx directory
sudo cp -r dist/* /var/www/html/
```

#### 4. Environment Configuration
```bash
# Production environment file
sudo -u sopbuilder cat > /home/<USER>/app/sop-templates/.env.prod << EOF
# Database
DATABASE_URL=postgresql://sopbuilder:secure_password_here@localhost/sopbuilder_prod

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_here

# LLM Providers (preserve existing free providers)
OPENROUTER_API_KEY=your_openrouter_key_here
GROQ_API_KEY=your_groq_key_here
HUGGINGFACE_API_KEY=your_huggingface_key_here

# File storage
UPLOAD_DIR=/home/<USER>/app/uploads
OUTPUT_DIR=/home/<USER>/app/outputs

# Security
ALLOWED_HOSTS=nextlevelsbs.com,www.nextlevelsbs.com
CORS_ORIGINS=https://nextlevelsbs.com,https://www.nextlevelsbs.com
EOF
```

#### 5. Nginx Configuration
```nginx
# /etc/nginx/sites-available/nextlevelsbs.com
server {
    listen 80;
    server_name nextlevelsbs.com www.nextlevelsbs.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name nextlevelsbs.com www.nextlevelsbs.com;

    ssl_certificate /etc/letsencrypt/live/nextlevelsbs.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/nextlevelsbs.com/privkey.pem;

    # Frontend (React app)
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API endpoints
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # WebSocket endpoints
    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # File downloads
    location /downloads/ {
        alias /home/<USER>/app/outputs/pdfs/;
        add_header Content-Disposition attachment;
        add_header X-Content-Type-Options nosniff;
    }
}
```

#### 6. Process Management (Supervisor)
```ini
# /etc/supervisor/conf.d/sopbuilder.conf
[program:sopbuilder_api]
command=/home/<USER>/app/sop-templates/venv/bin/gunicorn api.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
directory=/home/<USER>/app/sop-templates
user=sopbuilder
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/sopbuilder_api.log

[program:sopbuilder_worker]
command=/home/<USER>/app/sop-templates/venv/bin/celery -A api.background_tasks worker --loglevel=info
directory=/home/<USER>/app/sop-templates
user=sopbuilder
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/sopbuilder_worker.log
```

#### 7. SSL Certificate Setup
```bash
# Get SSL certificate
sudo certbot --nginx -d nextlevelsbs.com -d www.nextlevelsbs.com

# Set up auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 3. 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          cd sop-templates
          pip install -r requirements.txt
          pip install pytest
      - name: Run tests
        run: |
          cd sop-templates
          pytest tests/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /home/<USER>/app
            git pull origin main
            cd sop-templates/sop-wizard-pro-main
            npm install
            npm run build
            sudo cp -r dist/* /var/www/html/
            sudo supervisorctl restart sopbuilder_api
            sudo supervisorctl restart sopbuilder_worker
```

## 4. 🔧 Environment Configuration

### Development (.env.local)
```env
# Database
DATABASE_URL=sqlite:///./dev.db

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
JWT_SECRET_KEY=dev_secret_key_not_for_production

# LLM Providers
OPENROUTER_API_KEY=your_dev_key_here
GROQ_API_KEY=your_dev_key_here

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Debug
DEBUG=true
LOG_LEVEL=DEBUG
```

### Production (.env.prod)
```env
# Database
DATABASE_URL=postgresql://sopbuilder:password@localhost/sopbuilder_prod

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
JWT_SECRET_KEY=super_secure_production_key

# LLM Providers (same as development - free providers)
OPENROUTER_API_KEY=your_production_key_here
GROQ_API_KEY=your_production_key_here

# Security
ALLOWED_HOSTS=nextlevelsbs.com,www.nextlevelsbs.com
CORS_ORIGINS=https://nextlevelsbs.com,https://www.nextlevelsbs.com

# Performance
WORKERS=4
MAX_REQUESTS=1000
TIMEOUT=30

# Monitoring
LOG_LEVEL=INFO
SENTRY_DSN=your_sentry_dsn_here
```

## 5. 📊 Monitoring & Maintenance

### Health Checks
```python
# api/health.py
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0",
        "services": {
            "database": await check_database(),
            "redis": await check_redis(),
            "llm_providers": await check_llm_providers()
        }
    }
```

### Backup Strategy
```bash
# Daily database backup
0 2 * * * pg_dump sopbuilder_prod | gzip > /backups/db_$(date +\%Y\%m\%d).sql.gz

# Weekly file backup
0 3 * * 0 tar -czf /backups/files_$(date +\%Y\%m\%d).tar.gz /home/<USER>/app/outputs
```

### Log Management
```bash
# Logrotate configuration
sudo cat > /etc/logrotate.d/sopbuilder << EOF
/var/log/sopbuilder*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 sopbuilder sopbuilder
}
EOF
```
