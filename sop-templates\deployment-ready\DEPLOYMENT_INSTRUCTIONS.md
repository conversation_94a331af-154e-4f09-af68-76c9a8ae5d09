# 🚀 SOP Builder MVP - Ready for nextlevelsbs.com Deployment

## 📦 **What's in This Package**

```
deployment-ready/
├── frontend/           ← Upload to Hostinger
│   ├── index.html     ← Main app file
│   ├── assets/        ← CSS and JavaScript files
│   └── ...
├── backend/           ← Deploy to cloud service
│   ├── api/           ← API server files
│   ├── scripts/       ← SOP generation scripts
│   └── requirements.txt ← Python dependencies
└── DEPLOYMENT_INSTRUCTIONS.md ← This file
```

## 🌐 **Step 1: Upload Frontend to Hostinger**

### **Access Hostinger File Manager**
1. Login to https://hpanel.hostinger.com
2. Go to "Files" → "File Manager"
3. Navigate to `public_html` (your website root)

### **Create SOP Directory**
1. Create new folder: `public_html/sop/`
2. This makes your app accessible at `nextlevelsbs.com/sop/`

### **Upload Frontend Files**
1. **Upload ALL files** from the `frontend/` folder
2. **Upload to**: `public_html/sop/`
3. **Important**: Make sure `index.html` is directly in the `sop` folder

**Your file structure should look like:**
```
public_html/
├── sop/
│   ├── index.html          ← Must be here!
│   ├── assets/
│   │   ├── index-[hash].js
│   │   ├── index-[hash].css
│   │   └── ...
│   ├── favicon.ico
│   └── robots.txt
└── (your existing website files)
```

## ☁️ **Step 2: Deploy Backend API**

### **Option A: Railway (Recommended - FREE)**

**Why Railway?**
- ✅ Free tier (500 hours/month)
- ✅ Easy setup
- ✅ Automatic deployments
- ✅ Built-in SSL

**Setup Steps:**

1. **Create GitHub Repository** (if you haven't already)
   - Go to https://github.com
   - Create new repository: "SOP-Builder-MVP"
   - Upload your `backend/` folder contents

2. **Deploy to Railway**
   - Go to https://railway.app
   - Sign up with GitHub
   - Click "New Project" → "Deploy from GitHub repo"
   - Select your repository
   - Railway auto-detects Python and deploys

3. **Configure Environment Variables**
   - In Railway dashboard, click "Variables"
   - Add: `OPENROUTER_API_KEY=your_key_here`
   - Add: `PORT=8000`

4. **Get Your API URL**
   - Railway provides: `https://your-app-name.railway.app`
   - Test: `https://your-app-name.railway.app/api/health`

### **Option B: Render (Alternative FREE)**

1. **Connect to Render**
   - Go to https://render.com
   - Sign up with GitHub
   - Click "New" → "Web Service"

2. **Configure**
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `python api/simple_server.py`
   - **Environment**: Python 3

### **Option C: DigitalOcean ($6/month)**

1. **Create Droplet**
   - Ubuntu 22.04, $6/month plan
   - Upload backend files via SSH
   - Install: `pip install -r requirements.txt`
   - Run: `python api/simple_server.py`

## 🔗 **Step 3: Connect Frontend to Backend**

After deploying your backend, update the frontend:

1. **Get your API URL** from Railway/Render/DigitalOcean
2. **Edit environment file**:
   ```bash
   # In sop-wizard-pro-main/.env.production
   VITE_API_BASE_URL=https://your-railway-app.railway.app
   ```
3. **Rebuild frontend**:
   ```bash
   cd sop-wizard-pro-main
   npm run build
   ```
4. **Re-upload** new `dist/` contents to Hostinger

## 🧪 **Step 4: Test Everything**

### **Test URLs**
- **Main App**: https://nextlevelsbs.com/sop/
- **Templates**: https://nextlevelsbs.com/sop/templates
- **API Health**: https://your-api-url.com/api/health

### **Test Checklist**
- ✅ Frontend loads without errors
- ✅ Templates page shows available SOPs
- ✅ Can start SOP generation
- ✅ API responds to health check
- ✅ No CORS errors in browser console

## 🔧 **Troubleshooting**

### **Frontend Issues**
- **404 Error**: Check file paths in Hostinger
- **Blank Page**: Check browser console for errors
- **Can't find files**: Ensure `index.html` is in `/sop/` root

### **Backend Issues**
- **API not responding**: Check Railway/Render logs
- **CORS errors**: Verify API URL in frontend
- **Generation fails**: Check OpenRouter API key

### **Quick Fixes**
```bash
# Test API directly
curl https://your-api-url.com/api/health

# Check browser console for errors
# Press F12 → Console tab

# Rebuild with correct API URL
cd sop-wizard-pro-main
echo "VITE_API_BASE_URL=https://your-actual-api-url.com" > .env.production
npm run build
# Re-upload dist/ contents to Hostinger
```

## 💰 **Cost Summary**

### **Free Option (Recommended)**
- **Hostinger**: $0 (existing)
- **Railway**: $0 (free tier)
- **Total**: $0/month

### **Professional Option**
- **Hostinger**: $0 (existing)
- **DigitalOcean**: $6/month
- **Total**: $6/month

## 🎉 **Success!**

Once deployed, your SOP Builder MVP will be live at:

**https://nextlevelsbs.com/sop/**

Your customers can:
- ✅ Browse professional SOP templates
- ✅ Generate custom SOPs with AI
- ✅ Download professional PDFs
- ✅ Use advanced brand customization
- ✅ Access regulatory-compliant content

## 📞 **Need Help?**

If you encounter issues:
1. Check browser console (F12)
2. Verify API health endpoint
3. Review deployment service logs
4. Test locally first

**Your SOP Builder MVP is ready for commercial deployment!** 🚀
