/**
 * Brand Customization Component
 * Allows users to customize company branding for SOPs
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Upload, 
  Palette, 
  Building, 
  Image as ImageIcon, 
  Save, 
  RotateCcw,
  Eye
} from 'lucide-react';
import { type BrandConfig } from '@/services/api';

interface BrandCustomizerProps {
  brandConfig: BrandConfig;
  onBrandConfigChange: (config: BrandConfig) => void;
  onLogoUpload?: (file: File) => Promise<string>;
  className?: string;
}

const BrandCustomizer: React.FC<BrandCustomizerProps> = ({
  brandConfig,
  onBrandConfigChange,
  onLogoUpload,
  className = ''
}) => {
  const [localConfig, setLocalConfig] = useState<BrandConfig>(brandConfig);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Update local config when prop changes
  useEffect(() => {
    setLocalConfig(brandConfig);
  }, [brandConfig]);

  // Handle color change
  const handleColorChange = (colorKey: keyof BrandConfig, value: string) => {
    const updatedConfig = { ...localConfig, [colorKey]: value };
    setLocalConfig(updatedConfig);
    onBrandConfigChange(updatedConfig);
  };

  // Handle text change
  const handleTextChange = (textKey: keyof BrandConfig, value: string) => {
    const updatedConfig = { ...localConfig, [textKey]: value };
    setLocalConfig(updatedConfig);
    onBrandConfigChange(updatedConfig);
  };

  // Handle logo file selection
  const handleLogoFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      setLogoFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Upload logo
  const handleLogoUpload = async () => {
    if (!logoFile || !onLogoUpload) return;

    setIsUploading(true);
    try {
      const logoUrl = await onLogoUpload(logoFile);
      const updatedConfig = { ...localConfig, logo_url: logoUrl };
      setLocalConfig(updatedConfig);
      onBrandConfigChange(updatedConfig);
      setLogoFile(null);
      setLogoPreview(null);
    } catch (error) {
      console.error('Logo upload failed:', error);
      alert('Failed to upload logo. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Reset to defaults
  const handleReset = () => {
    const defaultConfig: BrandConfig = {
      primary_color: '#2C3E50',
      secondary_color: '#3498DB',
      company_name: '',
      tagline: ''
    };
    setLocalConfig(defaultConfig);
    onBrandConfigChange(defaultConfig);
    setLogoFile(null);
    setLogoPreview(null);
  };

  return (
    <Card className={`brand-customizer ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Palette className="w-5 h-5" />
          <span>Brand Customization</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Company Information */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <Building className="w-4 h-4" />
            <h3 className="font-medium">Company Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="company-name">Company Name</Label>
              <Input
                id="company-name"
                value={localConfig.company_name || ''}
                onChange={(e) => handleTextChange('company_name', e.target.value)}
                placeholder="Your Company Name"
              />
            </div>
            
            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                value={localConfig.tagline || ''}
                onChange={(e) => handleTextChange('tagline', e.target.value)}
                placeholder="Your company tagline"
              />
            </div>
          </div>
        </div>

        {/* Logo Upload */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <ImageIcon className="w-4 h-4" />
            <h3 className="font-medium">Company Logo</h3>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Current logo or preview */}
            <div className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
              {logoPreview || localConfig.logo_url ? (
                <img
                  src={logoPreview || localConfig.logo_url}
                  alt="Logo preview"
                  className="w-full h-full object-contain rounded-lg"
                />
              ) : (
                <ImageIcon className="w-8 h-8 text-gray-400" />
              )}
            </div>
            
            {/* Upload controls */}
            <div className="flex-1 space-y-2">
              <div className="flex items-center space-x-2">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoFileChange}
                  className="flex-1"
                />
                {logoFile && (
                  <Button
                    onClick={handleLogoUpload}
                    disabled={isUploading}
                    size="sm"
                  >
                    <Upload className="w-4 h-4 mr-1" />
                    {isUploading ? 'Uploading...' : 'Upload'}
                  </Button>
                )}
              </div>
              <p className="text-xs text-gray-500">
                PNG, JPG, or SVG. Max 5MB. Recommended: 200x80px
              </p>
            </div>
          </div>
        </div>

        {/* Color Scheme */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <Palette className="w-4 h-4" />
            <h3 className="font-medium">Color Scheme</h3>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="primary-color">Primary Color</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="primary-color"
                  type="color"
                  value={localConfig.primary_color || '#2C3E50'}
                  onChange={(e) => handleColorChange('primary_color', e.target.value)}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={localConfig.primary_color || '#2C3E50'}
                  onChange={(e) => handleColorChange('primary_color', e.target.value)}
                  placeholder="#2C3E50"
                  className="flex-1"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="secondary-color">Secondary Color</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="secondary-color"
                  type="color"
                  value={localConfig.secondary_color || '#3498DB'}
                  onChange={(e) => handleColorChange('secondary_color', e.target.value)}
                  className="w-12 h-10 p-1 border rounded"
                />
                <Input
                  value={localConfig.secondary_color || '#3498DB'}
                  onChange={(e) => handleColorChange('secondary_color', e.target.value)}
                  placeholder="#3498DB"
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <Eye className="w-4 h-4" />
            <h3 className="font-medium">Brand Preview</h3>
          </div>
          
          <div 
            className="p-4 rounded-lg border"
            style={{ 
              backgroundColor: localConfig.primary_color || '#2C3E50',
              color: '#FFFFFF'
            }}
          >
            <div className="flex items-center space-x-3">
              {(logoPreview || localConfig.logo_url) && (
                <img
                  src={logoPreview || localConfig.logo_url}
                  alt="Logo"
                  className="w-12 h-12 object-contain bg-white rounded p-1"
                />
              )}
              <div>
                <h4 className="font-bold text-lg">
                  {localConfig.company_name || 'Your Company Name'}
                </h4>
                {localConfig.tagline && (
                  <p className="text-sm opacity-90">{localConfig.tagline}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleReset}
            className="flex items-center space-x-1"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset to Defaults</span>
          </Button>
          
          <div className="text-sm text-gray-500">
            Changes are applied automatically
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BrandCustomizer;
