/**
 * PDF Preview Component
 * Displays real-time PDF preview with professional formatting
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Download, Eye, AlertCircle, RefreshCw } from 'lucide-react';
import { useAPI, type BrandConfig } from '@/services/api';

interface PDFPreviewProps {
  templateData: any;
  brandConfig?: BrandConfig;
  onPreviewReady?: (previewUrl: string) => void;
  className?: string;
}

const PDFPreview: React.FC<PDFPreviewProps> = ({
  templateData,
  brandConfig,
  onPreviewReady,
  className = ''
}) => {
  const { api } = useAPI();
  const [previewUrl, setPreviewUrl] = useState<string>();
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string>();
  const [lastGenerated, setLastGenerated] = useState<string>();

  // Debounced preview generation
  const generatePreview = useCallback(async () => {
    if (!templateData || isGenerating) return;

    const dataHash = JSON.stringify({ templateData, brandConfig });
    if (dataHash === lastGenerated) return; // Skip if no changes

    setIsGenerating(true);
    setError(undefined);

    try {
      const response = await api.generatePreview(templateData, brandConfig);
      const previewDataUrl = `data:application/pdf;base64,${response.preview_base64}`;
      
      setPreviewUrl(previewDataUrl);
      setLastGenerated(dataHash);
      
      if (onPreviewReady) {
        onPreviewReady(previewDataUrl);
      }
    } catch (err) {
      console.error('Preview generation failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate preview');
    } finally {
      setIsGenerating(false);
    }
  }, [templateData, brandConfig, api, isGenerating, lastGenerated, onPreviewReady]);

  // Auto-generate preview when data changes
  useEffect(() => {
    const timeoutId = setTimeout(generatePreview, 1000); // 1 second debounce
    return () => clearTimeout(timeoutId);
  }, [generatePreview]);

  const handleRefresh = () => {
    setLastGenerated(''); // Force regeneration
    generatePreview();
  };

  const handleDownload = () => {
    if (previewUrl) {
      const link = document.createElement('a');
      link.href = previewUrl;
      link.download = `${templateData?.title || 'SOP'}_Preview.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <Card className={`pdf-preview-container ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center space-x-2">
            <Eye className="w-5 h-5" />
            <span>PDF Preview</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {isGenerating && (
              <div className="flex items-center space-x-2 text-blue-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">Generating...</span>
              </div>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isGenerating}
              className="flex items-center space-x-1"
            >
              <RefreshCw className={`w-4 h-4 ${isGenerating ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </Button>
            
            {previewUrl && (
              <Button
                variant="default"
                size="sm"
                onClick={handleDownload}
                className="flex items-center space-x-1"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="preview-content border-t">
          {error ? (
            <div className="flex flex-col items-center justify-center h-96 p-6 text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
              <h3 className="text-lg font-medium text-red-700 mb-2">Preview Error</h3>
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={handleRefresh} variant="outline">
                Try Again
              </Button>
            </div>
          ) : previewUrl ? (
            <div className="relative">
              <iframe
                src={previewUrl}
                className="w-full h-96 border-0"
                title="PDF Preview"
                loading="lazy"
                style={{ minHeight: '600px' }}
              />
              
              {/* Overlay for loading state */}
              {isGenerating && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                  <div className="flex flex-col items-center space-y-2">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                    <p className="text-sm text-gray-600">Updating preview...</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-96 p-6 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                <Eye className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">PDF Preview</h3>
              <p className="text-gray-500 mb-4">
                {isGenerating 
                  ? 'Generating your PDF preview...' 
                  : 'Preview will appear here when template data is available'
                }
              </p>
              {isGenerating && (
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PDFPreview;
