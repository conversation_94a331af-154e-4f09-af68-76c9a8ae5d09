# SOP Template Automation System - Quick Start Guide

## Overview
This system automates the creation, maintenance, and distribution of premium SOP templates using AI and automation tools. It's designed to run with just 1 hour of daily maintenance.

## Initial Setup (One-Time - 2 Hours)

### 1. Environment Setup
```bash
# Clone or navigate to the project
cd C:\Projects\SOP-Builder-MVP\sop-templates

# Install dependencies
pip install -r requirements.txt

# Copy and configure environment variables
cp .env.example .env
# Edit .env with your API keys
```

### 2. Required API Keys
- **OpenAI/Claude**: For content generation
- **Google Sheets**: For compliance tracking
- **Canva Pro**: Already have this!
- **Gumroad**: For distribution
- **Mailchimp/ConvertKit**: Email automation

### 3. Create Base Assets
```bash
# Create your brand assets in Canva Pro
# Save logo as: designs/assets/logo.png
# Create CTA background: designs/assets/cta_background.png
```

## Daily Workflow (1 Hour)

### Morning Routine (20 minutes)
```bash
# 1. Check for compliance updates
python scripts/automation/daily_update.py

# 2. Review staging area
ls outputs/staging/

# 3. Approve updates in Google Sheets
# Open your compliance tracking sheet
```

### Content Generation (30 minutes)
```bash
# 1. Generate new template content
python scripts/generators/sop_generator.py --type restaurant

# 2. Convert to PDF
python scripts/generators/pdf_generator.py --input outputs/templates/restaurant_*.json

# 3. Quick quality check
# Open PDF and verify formatting
```

### Deployment (10 minutes)
```bash
# 1. Deploy to Gumroad
python scripts/automation/deploy.py --staging-file outputs/staging/updates_*.json

# 2. Check deployment log
tail deployments.log
```

## Weekly Tasks (2 Hours)

### Monday: New Template Creation
```bash
# Generate industry variation
python scripts/generators/sop_generator.py --type restaurant --industry-data '{"name": "Food Truck", "special_requirements": ["mobile operations", "limited space"]}'
```

### Wednesday: Video Creation
```bash
# Generate promotional video
python scripts/generators/video_generator.py --type restaurant --method auto

# Or create OBS scenes for manual recording
python scripts/generators/video_generator.py --type restaurant --method obs
```

### Friday: Analytics & Planning
- Review Gumroad sales data
- Check customer feedback
- Plan next week's content

## Quick Command Reference

### Generate All Templates
```bash
for type in restaurant healthcare it-onboarding customer-service; do
    python scripts/generators/sop_generator.py --type $type
done
```

### Batch PDF Generation
```bash
for json in outputs/templates/*.json; do
    python scripts/generators/pdf_generator.py --input $json
done
```

### Test Deployment (No Upload)
```bash
python scripts/automation/deploy.py --test --staging-file outputs/staging/updates_20240101.json
```

## AI Prompt Templates

### Restaurant SOP Section
```
Create a detailed SOP section for [SECTION_NAME] in a restaurant setting.

Requirements:
- Include FDA Food Code 2022 citations
- Add specific temperature requirements
- Include critical control points
- Provide both English and Spanish key terms
- Add common violations to avoid

Format as actionable steps with clear measurements and timelines.
```

### Healthcare HIPAA Section
```
Create a HIPAA-compliant SOP section for [SECTION_NAME].

Requirements:
- Reference specific HIPAA rules (Privacy, Security, Breach)
- Include 2025 updates
- Add documentation requirements
- Include audit trail considerations
- Provide sample forms where applicable

Ensure all procedures meet OCR audit standards.
```

## Canva Pro Integration

### Template Design Workflow
1. Create base template in Canva with your brand colors
2. Export as PDF template
3. Use exported design as base for `pdf_generator.py`
4. Key elements to include:
   - QR code placeholders
   - Checklist boxes
   - Color-coded sections
   - Footer with version info

### Quick Canva Templates
- Title Page: Use "Professional Report Cover"
- Section Headers: Use "Modern Heading" designs
- Checklists: Use "Task List" templates
- Infographics: Use "Process Flow" templates

## Troubleshooting

### Common Issues

**Import Errors**
```bash
# Ensure you're in the right directory
cd C:\Projects\SOP-Builder-MVP\sop-templates
# Reinstall dependencies
pip install -r requirements.txt --upgrade
```

**API Rate Limits**
- OpenAI: Max 60 requests/minute
- Solution: Add delays in `sop_generator.py`

**PDF Generation Issues**
```bash
# Install system fonts
# Windows: Install Arial, Helvetica
# Or modify pdf_generator.py to use default fonts
```

**Gumroad Upload Fails**
- Check API token is valid
- Ensure product IDs are correct in .env
- Verify file size < 250MB

## Monitoring & Maintenance

### Daily Health Check
```python
# Add to scripts/automation/health_check.py
import requests
import json
from datetime import datetime

def check_system_health():
    checks = {
        'api_keys': check_api_keys(),
        'disk_space': check_disk_space(),
        'pending_updates': check_pending_updates(),
        'last_deployment': check_last_deployment()
    }
    
    print(f"System Health Check - {datetime.now()}")
    for check, status in checks.items():
        print(f"{check}: {'✅' if status else '❌'}")
```

### Weekly Metrics
- Templates generated
- Updates deployed
- Customer notifications sent
- Error rate
- Generation time

## Scaling Tips

### Adding New Industries
1. Create new compliance YAML in `data/compliance/`
2. Add prompts to `prompts/[industry]_prompts.json`
3. Update video scripts in `video_generator.py`
4. Add to Gumroad product mapping

### Automation Improvements
- Use GitHub Actions for daily runs
- Implement Zapier/Make.com webhooks
- Add Slack notifications for errors
- Create customer portal for instant access

## Revenue Optimization

### Upsell Opportunities
1. **Audit Service**: Review customer's existing SOPs
2. **Custom Templates**: Industry-specific modifications
3. **Training Videos**: Premium video walkthroughs
4. **Consultation Calls**: Monthly Q&A sessions

### Bundle Strategies
- Starter Pack: 4 templates @ $49
- Professional: All templates + updates @ $97
- Enterprise: Unlimited customization @ $197

## Support Automation

### FAQ Responses
Create `templates/faq_responses.json` with common questions:
```json
{
  "update_frequency": "We update templates monthly or whenever regulations change",
  "customization": "Yes, all templates are fully customizable in Word/PDF",
  "refund_policy": "30-day money-back guarantee if templates don't meet your needs"
}
```

### Auto-Responder Setup
Use Gumroad's webhook to trigger:
1. Welcome email with quick start guide
2. 7-day follow-up with tips
3. 30-day check-in for feedback

## Future Enhancements

### Phase 2 (Month 2-3)
- Add Spanish language versions
- Implement A/B testing for pricing
- Create mobile app checklists
- Add video tutorials

### Phase 3 (Month 4-6)
- White-label partner program
- API for enterprise customers
- Compliance tracking dashboard
- Automated audit reports

## Emergency Procedures

### If Compliance Update Missed
```bash
# Run immediate check
python scripts/automation/daily_update.py --force

# Generate emergency updates
python scripts/generators/sop_generator.py --type all --emergency

# Deploy with priority flag
python scripts/automation/deploy.py --priority --notify-all
```

### If System Down
1. Check error logs: `tail -f *.log`
2. Verify API keys are valid
3. Run health check script
4. Fallback to manual PDF creation in Canva

Remember: The goal is to spend only 1 hour per day. Use automation to handle repetitive tasks and focus your time on quality control and customer relationships.
