var Fv=Object.defineProperty;var Ku=e=>{throw TypeError(e)};var zv=(e,t,n)=>t in e?Fv(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var qu=(e,t,n)=>zv(e,typeof t!="symbol"?t+"":t,n),Ta=(e,t,n)=>t.has(e)||Ku("Cannot "+n);var P=(e,t,n)=>(Ta(e,t,"read from private field"),n?n.call(e):t.get(e)),J=(e,t,n)=>t.has(e)?Ku("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),G=(e,t,n,r)=>(Ta(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Ae=(e,t,n)=>(Ta(e,t,"access private method"),n);var Ts=(e,t,n,r)=>({set _(o){G(e,t,o,n)},get _(){return P(e,t,r)}});function $v(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function Jf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ep={exports:{}},Qi={},tp={exports:{}},K={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gs=Symbol.for("react.element"),Bv=Symbol.for("react.portal"),Uv=Symbol.for("react.fragment"),Vv=Symbol.for("react.strict_mode"),Hv=Symbol.for("react.profiler"),Wv=Symbol.for("react.provider"),Gv=Symbol.for("react.context"),Qv=Symbol.for("react.forward_ref"),Kv=Symbol.for("react.suspense"),qv=Symbol.for("react.memo"),Yv=Symbol.for("react.lazy"),Yu=Symbol.iterator;function Xv(e){return e===null||typeof e!="object"?null:(e=Yu&&e[Yu]||e["@@iterator"],typeof e=="function"?e:null)}var np={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},rp=Object.assign,op={};function lo(e,t,n){this.props=e,this.context=t,this.refs=op,this.updater=n||np}lo.prototype.isReactComponent={};lo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function sp(){}sp.prototype=lo.prototype;function Nc(e,t,n){this.props=e,this.context=t,this.refs=op,this.updater=n||np}var Ec=Nc.prototype=new sp;Ec.constructor=Nc;rp(Ec,lo.prototype);Ec.isPureReactComponent=!0;var Xu=Array.isArray,ip=Object.prototype.hasOwnProperty,bc={current:null},ap={key:!0,ref:!0,__self:!0,__source:!0};function lp(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)ip.call(t,r)&&!ap.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var c=Array(a),u=0;u<a;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:gs,type:e,key:s,ref:i,props:o,_owner:bc.current}}function Zv(e,t){return{$$typeof:gs,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function jc(e){return typeof e=="object"&&e!==null&&e.$$typeof===gs}function Jv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Zu=/\/+/g;function Ra(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Jv(""+e.key):t.toString(36)}function Zs(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case gs:case Bv:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+Ra(i,0):r,Xu(o)?(n="",e!=null&&(n=e.replace(Zu,"$&/")+"/"),Zs(o,t,n,"",function(u){return u})):o!=null&&(jc(o)&&(o=Zv(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Zu,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",Xu(e))for(var a=0;a<e.length;a++){s=e[a];var c=r+Ra(s,a);i+=Zs(s,t,n,c,o)}else if(c=Xv(e),typeof c=="function")for(e=c.call(e),a=0;!(s=e.next()).done;)s=s.value,c=r+Ra(s,a++),i+=Zs(s,t,n,c,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Rs(e,t,n){if(e==null)return e;var r=[],o=0;return Zs(e,r,"","",function(s){return t.call(n,s,o++)}),r}function ey(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var He={current:null},Js={transition:null},ty={ReactCurrentDispatcher:He,ReactCurrentBatchConfig:Js,ReactCurrentOwner:bc};function cp(){throw Error("act(...) is not supported in production builds of React.")}K.Children={map:Rs,forEach:function(e,t,n){Rs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Rs(e,function(){t++}),t},toArray:function(e){return Rs(e,function(t){return t})||[]},only:function(e){if(!jc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};K.Component=lo;K.Fragment=Uv;K.Profiler=Hv;K.PureComponent=Nc;K.StrictMode=Vv;K.Suspense=Kv;K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ty;K.act=cp;K.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=rp({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=bc.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)ip.call(t,c)&&!ap.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){a=Array(c);for(var u=0;u<c;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:gs,type:e.type,key:o,ref:s,props:r,_owner:i}};K.createContext=function(e){return e={$$typeof:Gv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Wv,_context:e},e.Consumer=e};K.createElement=lp;K.createFactory=function(e){var t=lp.bind(null,e);return t.type=e,t};K.createRef=function(){return{current:null}};K.forwardRef=function(e){return{$$typeof:Qv,render:e}};K.isValidElement=jc;K.lazy=function(e){return{$$typeof:Yv,_payload:{_status:-1,_result:e},_init:ey}};K.memo=function(e,t){return{$$typeof:qv,type:e,compare:t===void 0?null:t}};K.startTransition=function(e){var t=Js.transition;Js.transition={};try{e()}finally{Js.transition=t}};K.unstable_act=cp;K.useCallback=function(e,t){return He.current.useCallback(e,t)};K.useContext=function(e){return He.current.useContext(e)};K.useDebugValue=function(){};K.useDeferredValue=function(e){return He.current.useDeferredValue(e)};K.useEffect=function(e,t){return He.current.useEffect(e,t)};K.useId=function(){return He.current.useId()};K.useImperativeHandle=function(e,t,n){return He.current.useImperativeHandle(e,t,n)};K.useInsertionEffect=function(e,t){return He.current.useInsertionEffect(e,t)};K.useLayoutEffect=function(e,t){return He.current.useLayoutEffect(e,t)};K.useMemo=function(e,t){return He.current.useMemo(e,t)};K.useReducer=function(e,t,n){return He.current.useReducer(e,t,n)};K.useRef=function(e){return He.current.useRef(e)};K.useState=function(e){return He.current.useState(e)};K.useSyncExternalStore=function(e,t,n){return He.current.useSyncExternalStore(e,t,n)};K.useTransition=function(){return He.current.useTransition()};K.version="18.3.1";tp.exports=K;var g=tp.exports;const M=Jf(g),up=$v({__proto__:null,default:M},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ny=g,ry=Symbol.for("react.element"),oy=Symbol.for("react.fragment"),sy=Object.prototype.hasOwnProperty,iy=ny.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ay={key:!0,ref:!0,__self:!0,__source:!0};function dp(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)sy.call(t,r)&&!ay.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ry,type:e,key:s,ref:i,props:o,_owner:iy.current}}Qi.Fragment=oy;Qi.jsx=dp;Qi.jsxs=dp;ep.exports=Qi;var l=ep.exports,fp={exports:{}},lt={},pp={exports:{}},hp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,A){var z=E.length;E.push(A);e:for(;0<z;){var D=z-1>>>1,U=E[D];if(0<o(U,A))E[D]=A,E[z]=U,z=D;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var A=E[0],z=E.pop();if(z!==A){E[0]=z;e:for(var D=0,U=E.length,q=U>>>1;D<q;){var de=2*(D+1)-1,Ze=E[de],te=de+1,yt=E[te];if(0>o(Ze,z))te<U&&0>o(yt,Ze)?(E[D]=yt,E[te]=z,D=te):(E[D]=Ze,E[de]=z,D=de);else if(te<U&&0>o(yt,z))E[D]=yt,E[te]=z,D=te;else break e}}return A}function o(E,A){var z=E.sortIndex-A.sortIndex;return z!==0?z:E.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var c=[],u=[],f=1,p=null,d=3,x=!1,w=!1,y=!1,C=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(E){for(var A=n(u);A!==null;){if(A.callback===null)r(u);else if(A.startTime<=E)r(u),A.sortIndex=A.expirationTime,t(c,A);else break;A=n(u)}}function S(E){if(y=!1,v(E),!w)if(n(c)!==null)w=!0,B(N);else{var A=n(u);A!==null&&Q(S,A.startTime-E)}}function N(E,A){w=!1,y&&(y=!1,m(j),j=-1),x=!0;var z=d;try{for(v(A),p=n(c);p!==null&&(!(p.expirationTime>A)||E&&!$());){var D=p.callback;if(typeof D=="function"){p.callback=null,d=p.priorityLevel;var U=D(p.expirationTime<=A);A=e.unstable_now(),typeof U=="function"?p.callback=U:p===n(c)&&r(c),v(A)}else r(c);p=n(c)}if(p!==null)var q=!0;else{var de=n(u);de!==null&&Q(S,de.startTime-A),q=!1}return q}finally{p=null,d=z,x=!1}}var b=!1,k=null,j=-1,R=5,O=-1;function $(){return!(e.unstable_now()-O<R)}function T(){if(k!==null){var E=e.unstable_now();O=E;var A=!0;try{A=k(!0,E)}finally{A?F():(b=!1,k=null)}}else b=!1}var F;if(typeof h=="function")F=function(){h(T)};else if(typeof MessageChannel<"u"){var L=new MessageChannel,V=L.port2;L.port1.onmessage=T,F=function(){V.postMessage(null)}}else F=function(){C(T,0)};function B(E){k=E,b||(b=!0,F())}function Q(E,A){j=C(function(){E(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){w||x||(w=!0,B(N))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(E){switch(d){case 1:case 2:case 3:var A=3;break;default:A=d}var z=d;d=A;try{return E()}finally{d=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,A){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var z=d;d=E;try{return A()}finally{d=z}},e.unstable_scheduleCallback=function(E,A,z){var D=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?D+z:D):z=D,E){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=z+U,E={id:f++,callback:A,priorityLevel:E,startTime:z,expirationTime:U,sortIndex:-1},z>D?(E.sortIndex=z,t(u,E),n(c)===null&&E===n(u)&&(y?(m(j),j=-1):y=!0,Q(S,z-D))):(E.sortIndex=U,t(c,E),w||x||(w=!0,B(N))),E},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(E){var A=d;return function(){var z=d;d=A;try{return E.apply(this,arguments)}finally{d=z}}}})(hp);pp.exports=hp;var ly=pp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cy=g,it=ly;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var mp=new Set,Uo={};function mr(e,t){Jr(e,t),Jr(e+"Capture",t)}function Jr(e,t){for(Uo[e]=t,e=0;e<t.length;e++)mp.add(t[e])}var Xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fl=Object.prototype.hasOwnProperty,uy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ju={},ed={};function dy(e){return fl.call(ed,e)?!0:fl.call(Ju,e)?!1:uy.test(e)?ed[e]=!0:(Ju[e]=!0,!1)}function fy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function py(e,t,n,r){if(t===null||typeof t>"u"||fy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function We(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Oe[e]=new We(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Oe[t]=new We(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Oe[e]=new We(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Oe[e]=new We(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Oe[e]=new We(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Oe[e]=new We(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Oe[e]=new We(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Oe[e]=new We(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Oe[e]=new We(e,5,!1,e.toLowerCase(),null,!1,!1)});var kc=/[\-:]([a-z])/g;function Pc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(kc,Pc);Oe[t]=new We(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(kc,Pc);Oe[t]=new We(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(kc,Pc);Oe[t]=new We(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Oe[e]=new We(e,1,!1,e.toLowerCase(),null,!1,!1)});Oe.xlinkHref=new We("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Oe[e]=new We(e,1,!1,e.toLowerCase(),null,!0,!0)});function Tc(e,t,n,r){var o=Oe.hasOwnProperty(t)?Oe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(py(t,n,o,r)&&(n=null),r||o===null?dy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var rn=cy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_s=Symbol.for("react.element"),Er=Symbol.for("react.portal"),br=Symbol.for("react.fragment"),Rc=Symbol.for("react.strict_mode"),pl=Symbol.for("react.profiler"),gp=Symbol.for("react.provider"),vp=Symbol.for("react.context"),_c=Symbol.for("react.forward_ref"),hl=Symbol.for("react.suspense"),ml=Symbol.for("react.suspense_list"),Oc=Symbol.for("react.memo"),mn=Symbol.for("react.lazy"),yp=Symbol.for("react.offscreen"),td=Symbol.iterator;function yo(e){return e===null||typeof e!="object"?null:(e=td&&e[td]||e["@@iterator"],typeof e=="function"?e:null)}var he=Object.assign,_a;function Po(e){if(_a===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);_a=t&&t[1]||""}return`
`+_a+e}var Oa=!1;function Aa(e,t){if(!e||Oa)return"";Oa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,a=s.length-1;1<=i&&0<=a&&o[i]!==s[a];)a--;for(;1<=i&&0<=a;i--,a--)if(o[i]!==s[a]){if(i!==1||a!==1)do if(i--,a--,0>a||o[i]!==s[a]){var c=`
`+o[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{Oa=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Po(e):""}function hy(e){switch(e.tag){case 5:return Po(e.type);case 16:return Po("Lazy");case 13:return Po("Suspense");case 19:return Po("SuspenseList");case 0:case 2:case 15:return e=Aa(e.type,!1),e;case 11:return e=Aa(e.type.render,!1),e;case 1:return e=Aa(e.type,!0),e;default:return""}}function gl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case br:return"Fragment";case Er:return"Portal";case pl:return"Profiler";case Rc:return"StrictMode";case hl:return"Suspense";case ml:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case vp:return(e.displayName||"Context")+".Consumer";case gp:return(e._context.displayName||"Context")+".Provider";case _c:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Oc:return t=e.displayName||null,t!==null?t:gl(e.type)||"Memo";case mn:t=e._payload,e=e._init;try{return gl(e(t))}catch{}}return null}function my(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gl(t);case 8:return t===Rc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gy(e){var t=xp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Os(e){e._valueTracker||(e._valueTracker=gy(e))}function wp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=xp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function vl(e,t){var n=t.checked;return he({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function nd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Dn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Cp(e,t){t=t.checked,t!=null&&Tc(e,"checked",t,!1)}function yl(e,t){Cp(e,t);var n=Dn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?xl(e,t.type,n):t.hasOwnProperty("defaultValue")&&xl(e,t.type,Dn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function xl(e,t,n){(t!=="number"||pi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var To=Array.isArray;function Ir(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Dn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function wl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return he({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function od(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(To(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dn(n)}}function Sp(e,t){var n=Dn(t.value),r=Dn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function sd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Np(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Cl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Np(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var As,Ep=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(As=As||document.createElement("div"),As.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=As.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Oo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vy=["Webkit","ms","Moz","O"];Object.keys(Oo).forEach(function(e){vy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Oo[t]=Oo[e]})});function bp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Oo.hasOwnProperty(e)&&Oo[e]?(""+t).trim():t+"px"}function jp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=bp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var yy=he({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Sl(e,t){if(t){if(yy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function Nl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var El=null;function Ac(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bl=null,Dr=null,Fr=null;function id(e){if(e=xs(e)){if(typeof bl!="function")throw Error(_(280));var t=e.stateNode;t&&(t=Zi(t),bl(e.stateNode,e.type,t))}}function kp(e){Dr?Fr?Fr.push(e):Fr=[e]:Dr=e}function Pp(){if(Dr){var e=Dr,t=Fr;if(Fr=Dr=null,id(e),t)for(e=0;e<t.length;e++)id(t[e])}}function Tp(e,t){return e(t)}function Rp(){}var Ma=!1;function _p(e,t,n){if(Ma)return e(t,n);Ma=!0;try{return Tp(e,t,n)}finally{Ma=!1,(Dr!==null||Fr!==null)&&(Rp(),Pp())}}function Ho(e,t){var n=e.stateNode;if(n===null)return null;var r=Zi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var jl=!1;if(Xt)try{var xo={};Object.defineProperty(xo,"passive",{get:function(){jl=!0}}),window.addEventListener("test",xo,xo),window.removeEventListener("test",xo,xo)}catch{jl=!1}function xy(e,t,n,r,o,s,i,a,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var Ao=!1,hi=null,mi=!1,kl=null,wy={onError:function(e){Ao=!0,hi=e}};function Cy(e,t,n,r,o,s,i,a,c){Ao=!1,hi=null,xy.apply(wy,arguments)}function Sy(e,t,n,r,o,s,i,a,c){if(Cy.apply(this,arguments),Ao){if(Ao){var u=hi;Ao=!1,hi=null}else throw Error(_(198));mi||(mi=!0,kl=u)}}function gr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Op(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ad(e){if(gr(e)!==e)throw Error(_(188))}function Ny(e){var t=e.alternate;if(!t){if(t=gr(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return ad(o),e;if(s===r)return ad(o),t;s=s.sibling}throw Error(_(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i){for(a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Ap(e){return e=Ny(e),e!==null?Mp(e):null}function Mp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Mp(e);if(t!==null)return t;e=e.sibling}return null}var Lp=it.unstable_scheduleCallback,ld=it.unstable_cancelCallback,Ey=it.unstable_shouldYield,by=it.unstable_requestPaint,ye=it.unstable_now,jy=it.unstable_getCurrentPriorityLevel,Mc=it.unstable_ImmediatePriority,Ip=it.unstable_UserBlockingPriority,gi=it.unstable_NormalPriority,ky=it.unstable_LowPriority,Dp=it.unstable_IdlePriority,Ki=null,zt=null;function Py(e){if(zt&&typeof zt.onCommitFiberRoot=="function")try{zt.onCommitFiberRoot(Ki,e,void 0,(e.current.flags&128)===128)}catch{}}var bt=Math.clz32?Math.clz32:_y,Ty=Math.log,Ry=Math.LN2;function _y(e){return e>>>=0,e===0?32:31-(Ty(e)/Ry|0)|0}var Ms=64,Ls=4194304;function Ro(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function vi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~o;a!==0?r=Ro(a):(s&=i,s!==0&&(r=Ro(s)))}else i=n&~o,i!==0?r=Ro(i):s!==0&&(r=Ro(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-bt(t),o=1<<n,r|=e[n],t&=~o;return r}function Oy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ay(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-bt(s),a=1<<i,c=o[i];c===-1?(!(a&n)||a&r)&&(o[i]=Oy(a,t)):c<=t&&(e.expiredLanes|=a),s&=~a}}function Pl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Fp(){var e=Ms;return Ms<<=1,!(Ms&4194240)&&(Ms=64),e}function La(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-bt(t),e[t]=n}function My(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-bt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function Lc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-bt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ne=0;function zp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var $p,Ic,Bp,Up,Vp,Tl=!1,Is=[],kn=null,Pn=null,Tn=null,Wo=new Map,Go=new Map,vn=[],Ly="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function cd(e,t){switch(e){case"focusin":case"focusout":kn=null;break;case"dragenter":case"dragleave":Pn=null;break;case"mouseover":case"mouseout":Tn=null;break;case"pointerover":case"pointerout":Wo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Go.delete(t.pointerId)}}function wo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=xs(t),t!==null&&Ic(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Iy(e,t,n,r,o){switch(t){case"focusin":return kn=wo(kn,e,t,n,r,o),!0;case"dragenter":return Pn=wo(Pn,e,t,n,r,o),!0;case"mouseover":return Tn=wo(Tn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return Wo.set(s,wo(Wo.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,Go.set(s,wo(Go.get(s)||null,e,t,n,r,o)),!0}return!1}function Hp(e){var t=Zn(e.target);if(t!==null){var n=gr(t);if(n!==null){if(t=n.tag,t===13){if(t=Op(n),t!==null){e.blockedOn=t,Vp(e.priority,function(){Bp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ei(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Rl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);El=r,n.target.dispatchEvent(r),El=null}else return t=xs(n),t!==null&&Ic(t),e.blockedOn=n,!1;t.shift()}return!0}function ud(e,t,n){ei(e)&&n.delete(t)}function Dy(){Tl=!1,kn!==null&&ei(kn)&&(kn=null),Pn!==null&&ei(Pn)&&(Pn=null),Tn!==null&&ei(Tn)&&(Tn=null),Wo.forEach(ud),Go.forEach(ud)}function Co(e,t){e.blockedOn===t&&(e.blockedOn=null,Tl||(Tl=!0,it.unstable_scheduleCallback(it.unstable_NormalPriority,Dy)))}function Qo(e){function t(o){return Co(o,e)}if(0<Is.length){Co(Is[0],e);for(var n=1;n<Is.length;n++){var r=Is[n];r.blockedOn===e&&(r.blockedOn=null)}}for(kn!==null&&Co(kn,e),Pn!==null&&Co(Pn,e),Tn!==null&&Co(Tn,e),Wo.forEach(t),Go.forEach(t),n=0;n<vn.length;n++)r=vn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<vn.length&&(n=vn[0],n.blockedOn===null);)Hp(n),n.blockedOn===null&&vn.shift()}var zr=rn.ReactCurrentBatchConfig,yi=!0;function Fy(e,t,n,r){var o=ne,s=zr.transition;zr.transition=null;try{ne=1,Dc(e,t,n,r)}finally{ne=o,zr.transition=s}}function zy(e,t,n,r){var o=ne,s=zr.transition;zr.transition=null;try{ne=4,Dc(e,t,n,r)}finally{ne=o,zr.transition=s}}function Dc(e,t,n,r){if(yi){var o=Rl(e,t,n,r);if(o===null)Wa(e,t,r,xi,n),cd(e,r);else if(Iy(o,e,t,n,r))r.stopPropagation();else if(cd(e,r),t&4&&-1<Ly.indexOf(e)){for(;o!==null;){var s=xs(o);if(s!==null&&$p(s),s=Rl(e,t,n,r),s===null&&Wa(e,t,r,xi,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else Wa(e,t,r,null,n)}}var xi=null;function Rl(e,t,n,r){if(xi=null,e=Ac(r),e=Zn(e),e!==null)if(t=gr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Op(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return xi=e,null}function Wp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(jy()){case Mc:return 1;case Ip:return 4;case gi:case ky:return 16;case Dp:return 536870912;default:return 16}default:return 16}}var En=null,Fc=null,ti=null;function Gp(){if(ti)return ti;var e,t=Fc,n=t.length,r,o="value"in En?En.value:En.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return ti=o.slice(e,1<r?1-r:void 0)}function ni(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ds(){return!0}function dd(){return!1}function ct(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Ds:dd,this.isPropagationStopped=dd,this}return he(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ds)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ds)},persist:function(){},isPersistent:Ds}),t}var co={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},zc=ct(co),ys=he({},co,{view:0,detail:0}),$y=ct(ys),Ia,Da,So,qi=he({},ys,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$c,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==So&&(So&&e.type==="mousemove"?(Ia=e.screenX-So.screenX,Da=e.screenY-So.screenY):Da=Ia=0,So=e),Ia)},movementY:function(e){return"movementY"in e?e.movementY:Da}}),fd=ct(qi),By=he({},qi,{dataTransfer:0}),Uy=ct(By),Vy=he({},ys,{relatedTarget:0}),Fa=ct(Vy),Hy=he({},co,{animationName:0,elapsedTime:0,pseudoElement:0}),Wy=ct(Hy),Gy=he({},co,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qy=ct(Gy),Ky=he({},co,{data:0}),pd=ct(Ky),qy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Yy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Xy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Zy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Xy[e])?!!t[e]:!1}function $c(){return Zy}var Jy=he({},ys,{key:function(e){if(e.key){var t=qy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ni(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Yy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$c,charCode:function(e){return e.type==="keypress"?ni(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ni(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),e0=ct(Jy),t0=he({},qi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hd=ct(t0),n0=he({},ys,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$c}),r0=ct(n0),o0=he({},co,{propertyName:0,elapsedTime:0,pseudoElement:0}),s0=ct(o0),i0=he({},qi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),a0=ct(i0),l0=[9,13,27,32],Bc=Xt&&"CompositionEvent"in window,Mo=null;Xt&&"documentMode"in document&&(Mo=document.documentMode);var c0=Xt&&"TextEvent"in window&&!Mo,Qp=Xt&&(!Bc||Mo&&8<Mo&&11>=Mo),md=" ",gd=!1;function Kp(e,t){switch(e){case"keyup":return l0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function qp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jr=!1;function u0(e,t){switch(e){case"compositionend":return qp(t);case"keypress":return t.which!==32?null:(gd=!0,md);case"textInput":return e=t.data,e===md&&gd?null:e;default:return null}}function d0(e,t){if(jr)return e==="compositionend"||!Bc&&Kp(e,t)?(e=Gp(),ti=Fc=En=null,jr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qp&&t.locale!=="ko"?null:t.data;default:return null}}var f0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function vd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!f0[e.type]:t==="textarea"}function Yp(e,t,n,r){kp(r),t=wi(t,"onChange"),0<t.length&&(n=new zc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Lo=null,Ko=null;function p0(e){ah(e,0)}function Yi(e){var t=Tr(e);if(wp(t))return e}function h0(e,t){if(e==="change")return t}var Xp=!1;if(Xt){var za;if(Xt){var $a="oninput"in document;if(!$a){var yd=document.createElement("div");yd.setAttribute("oninput","return;"),$a=typeof yd.oninput=="function"}za=$a}else za=!1;Xp=za&&(!document.documentMode||9<document.documentMode)}function xd(){Lo&&(Lo.detachEvent("onpropertychange",Zp),Ko=Lo=null)}function Zp(e){if(e.propertyName==="value"&&Yi(Ko)){var t=[];Yp(t,Ko,e,Ac(e)),_p(p0,t)}}function m0(e,t,n){e==="focusin"?(xd(),Lo=t,Ko=n,Lo.attachEvent("onpropertychange",Zp)):e==="focusout"&&xd()}function g0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Yi(Ko)}function v0(e,t){if(e==="click")return Yi(t)}function y0(e,t){if(e==="input"||e==="change")return Yi(t)}function x0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var kt=typeof Object.is=="function"?Object.is:x0;function qo(e,t){if(kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!fl.call(t,o)||!kt(e[o],t[o]))return!1}return!0}function wd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Cd(e,t){var n=wd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wd(n)}}function Jp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Jp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function eh(){for(var e=window,t=pi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=pi(e.document)}return t}function Uc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function w0(e){var t=eh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Jp(n.ownerDocument.documentElement,n)){if(r!==null&&Uc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Cd(n,s);var i=Cd(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var C0=Xt&&"documentMode"in document&&11>=document.documentMode,kr=null,_l=null,Io=null,Ol=!1;function Sd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ol||kr==null||kr!==pi(r)||(r=kr,"selectionStart"in r&&Uc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Io&&qo(Io,r)||(Io=r,r=wi(_l,"onSelect"),0<r.length&&(t=new zc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=kr)))}function Fs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Pr={animationend:Fs("Animation","AnimationEnd"),animationiteration:Fs("Animation","AnimationIteration"),animationstart:Fs("Animation","AnimationStart"),transitionend:Fs("Transition","TransitionEnd")},Ba={},th={};Xt&&(th=document.createElement("div").style,"AnimationEvent"in window||(delete Pr.animationend.animation,delete Pr.animationiteration.animation,delete Pr.animationstart.animation),"TransitionEvent"in window||delete Pr.transitionend.transition);function Xi(e){if(Ba[e])return Ba[e];if(!Pr[e])return e;var t=Pr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in th)return Ba[e]=t[n];return e}var nh=Xi("animationend"),rh=Xi("animationiteration"),oh=Xi("animationstart"),sh=Xi("transitionend"),ih=new Map,Nd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wn(e,t){ih.set(e,t),mr(t,[e])}for(var Ua=0;Ua<Nd.length;Ua++){var Va=Nd[Ua],S0=Va.toLowerCase(),N0=Va[0].toUpperCase()+Va.slice(1);Wn(S0,"on"+N0)}Wn(nh,"onAnimationEnd");Wn(rh,"onAnimationIteration");Wn(oh,"onAnimationStart");Wn("dblclick","onDoubleClick");Wn("focusin","onFocus");Wn("focusout","onBlur");Wn(sh,"onTransitionEnd");Jr("onMouseEnter",["mouseout","mouseover"]);Jr("onMouseLeave",["mouseout","mouseover"]);Jr("onPointerEnter",["pointerout","pointerover"]);Jr("onPointerLeave",["pointerout","pointerover"]);mr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));mr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));mr("onBeforeInput",["compositionend","keypress","textInput","paste"]);mr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));mr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));mr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _o="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),E0=new Set("cancel close invalid load scroll toggle".split(" ").concat(_o));function Ed(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Sy(r,t,void 0,e),e.currentTarget=null}function ah(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],c=a.instance,u=a.currentTarget;if(a=a.listener,c!==s&&o.isPropagationStopped())break e;Ed(o,a,u),s=c}else for(i=0;i<r.length;i++){if(a=r[i],c=a.instance,u=a.currentTarget,a=a.listener,c!==s&&o.isPropagationStopped())break e;Ed(o,a,u),s=c}}}if(mi)throw e=kl,mi=!1,kl=null,e}function ae(e,t){var n=t[Dl];n===void 0&&(n=t[Dl]=new Set);var r=e+"__bubble";n.has(r)||(lh(t,e,2,!1),n.add(r))}function Ha(e,t,n){var r=0;t&&(r|=4),lh(n,e,r,t)}var zs="_reactListening"+Math.random().toString(36).slice(2);function Yo(e){if(!e[zs]){e[zs]=!0,mp.forEach(function(n){n!=="selectionchange"&&(E0.has(n)||Ha(n,!1,e),Ha(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zs]||(t[zs]=!0,Ha("selectionchange",!1,t))}}function lh(e,t,n,r){switch(Wp(t)){case 1:var o=Fy;break;case 4:o=zy;break;default:o=Dc}n=o.bind(null,t,n,e),o=void 0,!jl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Wa(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===o||c.nodeType===8&&c.parentNode===o))return;i=i.return}for(;a!==null;){if(i=Zn(a),i===null)return;if(c=i.tag,c===5||c===6){r=s=i;continue e}a=a.parentNode}}r=r.return}_p(function(){var u=s,f=Ac(n),p=[];e:{var d=ih.get(e);if(d!==void 0){var x=zc,w=e;switch(e){case"keypress":if(ni(n)===0)break e;case"keydown":case"keyup":x=e0;break;case"focusin":w="focus",x=Fa;break;case"focusout":w="blur",x=Fa;break;case"beforeblur":case"afterblur":x=Fa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=fd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=Uy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=r0;break;case nh:case rh:case oh:x=Wy;break;case sh:x=s0;break;case"scroll":x=$y;break;case"wheel":x=a0;break;case"copy":case"cut":case"paste":x=Qy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=hd}var y=(t&4)!==0,C=!y&&e==="scroll",m=y?d!==null?d+"Capture":null:d;y=[];for(var h=u,v;h!==null;){v=h;var S=v.stateNode;if(v.tag===5&&S!==null&&(v=S,m!==null&&(S=Ho(h,m),S!=null&&y.push(Xo(h,S,v)))),C)break;h=h.return}0<y.length&&(d=new x(d,w,null,n,f),p.push({event:d,listeners:y}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",d&&n!==El&&(w=n.relatedTarget||n.fromElement)&&(Zn(w)||w[Zt]))break e;if((x||d)&&(d=f.window===f?f:(d=f.ownerDocument)?d.defaultView||d.parentWindow:window,x?(w=n.relatedTarget||n.toElement,x=u,w=w?Zn(w):null,w!==null&&(C=gr(w),w!==C||w.tag!==5&&w.tag!==6)&&(w=null)):(x=null,w=u),x!==w)){if(y=fd,S="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(y=hd,S="onPointerLeave",m="onPointerEnter",h="pointer"),C=x==null?d:Tr(x),v=w==null?d:Tr(w),d=new y(S,h+"leave",x,n,f),d.target=C,d.relatedTarget=v,S=null,Zn(f)===u&&(y=new y(m,h+"enter",w,n,f),y.target=v,y.relatedTarget=C,S=y),C=S,x&&w)t:{for(y=x,m=w,h=0,v=y;v;v=Sr(v))h++;for(v=0,S=m;S;S=Sr(S))v++;for(;0<h-v;)y=Sr(y),h--;for(;0<v-h;)m=Sr(m),v--;for(;h--;){if(y===m||m!==null&&y===m.alternate)break t;y=Sr(y),m=Sr(m)}y=null}else y=null;x!==null&&bd(p,d,x,y,!1),w!==null&&C!==null&&bd(p,C,w,y,!0)}}e:{if(d=u?Tr(u):window,x=d.nodeName&&d.nodeName.toLowerCase(),x==="select"||x==="input"&&d.type==="file")var N=h0;else if(vd(d))if(Xp)N=y0;else{N=g0;var b=m0}else(x=d.nodeName)&&x.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(N=v0);if(N&&(N=N(e,u))){Yp(p,N,n,f);break e}b&&b(e,d,u),e==="focusout"&&(b=d._wrapperState)&&b.controlled&&d.type==="number"&&xl(d,"number",d.value)}switch(b=u?Tr(u):window,e){case"focusin":(vd(b)||b.contentEditable==="true")&&(kr=b,_l=u,Io=null);break;case"focusout":Io=_l=kr=null;break;case"mousedown":Ol=!0;break;case"contextmenu":case"mouseup":case"dragend":Ol=!1,Sd(p,n,f);break;case"selectionchange":if(C0)break;case"keydown":case"keyup":Sd(p,n,f)}var k;if(Bc)e:{switch(e){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else jr?Kp(e,n)&&(j="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Qp&&n.locale!=="ko"&&(jr||j!=="onCompositionStart"?j==="onCompositionEnd"&&jr&&(k=Gp()):(En=f,Fc="value"in En?En.value:En.textContent,jr=!0)),b=wi(u,j),0<b.length&&(j=new pd(j,e,null,n,f),p.push({event:j,listeners:b}),k?j.data=k:(k=qp(n),k!==null&&(j.data=k)))),(k=c0?u0(e,n):d0(e,n))&&(u=wi(u,"onBeforeInput"),0<u.length&&(f=new pd("onBeforeInput","beforeinput",null,n,f),p.push({event:f,listeners:u}),f.data=k))}ah(p,t)})}function Xo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function wi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=Ho(e,n),s!=null&&r.unshift(Xo(e,s,o)),s=Ho(e,t),s!=null&&r.push(Xo(e,s,o))),e=e.return}return r}function Sr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function bd(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var a=n,c=a.alternate,u=a.stateNode;if(c!==null&&c===r)break;a.tag===5&&u!==null&&(a=u,o?(c=Ho(n,s),c!=null&&i.unshift(Xo(n,c,a))):o||(c=Ho(n,s),c!=null&&i.push(Xo(n,c,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var b0=/\r\n?/g,j0=/\u0000|\uFFFD/g;function jd(e){return(typeof e=="string"?e:""+e).replace(b0,`
`).replace(j0,"")}function $s(e,t,n){if(t=jd(t),jd(e)!==t&&n)throw Error(_(425))}function Ci(){}var Al=null,Ml=null;function Ll(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Il=typeof setTimeout=="function"?setTimeout:void 0,k0=typeof clearTimeout=="function"?clearTimeout:void 0,kd=typeof Promise=="function"?Promise:void 0,P0=typeof queueMicrotask=="function"?queueMicrotask:typeof kd<"u"?function(e){return kd.resolve(null).then(e).catch(T0)}:Il;function T0(e){setTimeout(function(){throw e})}function Ga(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Qo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Qo(t)}function Rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Pd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var uo=Math.random().toString(36).slice(2),Ft="__reactFiber$"+uo,Zo="__reactProps$"+uo,Zt="__reactContainer$"+uo,Dl="__reactEvents$"+uo,R0="__reactListeners$"+uo,_0="__reactHandles$"+uo;function Zn(e){var t=e[Ft];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Zt]||n[Ft]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Pd(e);e!==null;){if(n=e[Ft])return n;e=Pd(e)}return t}e=n,n=e.parentNode}return null}function xs(e){return e=e[Ft]||e[Zt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Tr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function Zi(e){return e[Zo]||null}var Fl=[],Rr=-1;function Gn(e){return{current:e}}function le(e){0>Rr||(e.current=Fl[Rr],Fl[Rr]=null,Rr--)}function oe(e,t){Rr++,Fl[Rr]=e.current,e.current=t}var Fn={},Fe=Gn(Fn),qe=Gn(!1),lr=Fn;function eo(e,t){var n=e.type.contextTypes;if(!n)return Fn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ye(e){return e=e.childContextTypes,e!=null}function Si(){le(qe),le(Fe)}function Td(e,t,n){if(Fe.current!==Fn)throw Error(_(168));oe(Fe,t),oe(qe,n)}function ch(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(_(108,my(e)||"Unknown",o));return he({},n,r)}function Ni(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Fn,lr=Fe.current,oe(Fe,e),oe(qe,qe.current),!0}function Rd(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=ch(e,t,lr),r.__reactInternalMemoizedMergedChildContext=e,le(qe),le(Fe),oe(Fe,e)):le(qe),oe(qe,n)}var Qt=null,Ji=!1,Qa=!1;function uh(e){Qt===null?Qt=[e]:Qt.push(e)}function O0(e){Ji=!0,uh(e)}function Qn(){if(!Qa&&Qt!==null){Qa=!0;var e=0,t=ne;try{var n=Qt;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Qt=null,Ji=!1}catch(o){throw Qt!==null&&(Qt=Qt.slice(e+1)),Lp(Mc,Qn),o}finally{ne=t,Qa=!1}}return null}var _r=[],Or=0,Ei=null,bi=0,dt=[],ft=0,cr=null,Kt=1,qt="";function Yn(e,t){_r[Or++]=bi,_r[Or++]=Ei,Ei=e,bi=t}function dh(e,t,n){dt[ft++]=Kt,dt[ft++]=qt,dt[ft++]=cr,cr=e;var r=Kt;e=qt;var o=32-bt(r)-1;r&=~(1<<o),n+=1;var s=32-bt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Kt=1<<32-bt(t)+o|n<<o|r,qt=s+e}else Kt=1<<s|n<<o|r,qt=e}function Vc(e){e.return!==null&&(Yn(e,1),dh(e,1,0))}function Hc(e){for(;e===Ei;)Ei=_r[--Or],_r[Or]=null,bi=_r[--Or],_r[Or]=null;for(;e===cr;)cr=dt[--ft],dt[ft]=null,qt=dt[--ft],dt[ft]=null,Kt=dt[--ft],dt[ft]=null}var ot=null,rt=null,ue=!1,Et=null;function fh(e,t){var n=pt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function _d(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ot=e,rt=Rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ot=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cr!==null?{id:Kt,overflow:qt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=pt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ot=e,rt=null,!0):!1;default:return!1}}function zl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $l(e){if(ue){var t=rt;if(t){var n=t;if(!_d(e,t)){if(zl(e))throw Error(_(418));t=Rn(n.nextSibling);var r=ot;t&&_d(e,t)?fh(r,n):(e.flags=e.flags&-4097|2,ue=!1,ot=e)}}else{if(zl(e))throw Error(_(418));e.flags=e.flags&-4097|2,ue=!1,ot=e}}}function Od(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ot=e}function Bs(e){if(e!==ot)return!1;if(!ue)return Od(e),ue=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ll(e.type,e.memoizedProps)),t&&(t=rt)){if(zl(e))throw ph(),Error(_(418));for(;t;)fh(e,t),t=Rn(t.nextSibling)}if(Od(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){rt=Rn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=ot?Rn(e.stateNode.nextSibling):null;return!0}function ph(){for(var e=rt;e;)e=Rn(e.nextSibling)}function to(){rt=ot=null,ue=!1}function Wc(e){Et===null?Et=[e]:Et.push(e)}var A0=rn.ReactCurrentBatchConfig;function No(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var a=o.refs;i===null?delete a[s]:a[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function Us(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ad(e){var t=e._init;return t(e._payload)}function hh(e){function t(m,h){if(e){var v=m.deletions;v===null?(m.deletions=[h],m.flags|=16):v.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function o(m,h){return m=Mn(m,h),m.index=0,m.sibling=null,m}function s(m,h,v){return m.index=v,e?(v=m.alternate,v!==null?(v=v.index,v<h?(m.flags|=2,h):v):(m.flags|=2,h)):(m.flags|=1048576,h)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,h,v,S){return h===null||h.tag!==6?(h=el(v,m.mode,S),h.return=m,h):(h=o(h,v),h.return=m,h)}function c(m,h,v,S){var N=v.type;return N===br?f(m,h,v.props.children,S,v.key):h!==null&&(h.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===mn&&Ad(N)===h.type)?(S=o(h,v.props),S.ref=No(m,h,v),S.return=m,S):(S=ci(v.type,v.key,v.props,null,m.mode,S),S.ref=No(m,h,v),S.return=m,S)}function u(m,h,v,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==v.containerInfo||h.stateNode.implementation!==v.implementation?(h=tl(v,m.mode,S),h.return=m,h):(h=o(h,v.children||[]),h.return=m,h)}function f(m,h,v,S,N){return h===null||h.tag!==7?(h=ir(v,m.mode,S,N),h.return=m,h):(h=o(h,v),h.return=m,h)}function p(m,h,v){if(typeof h=="string"&&h!==""||typeof h=="number")return h=el(""+h,m.mode,v),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case _s:return v=ci(h.type,h.key,h.props,null,m.mode,v),v.ref=No(m,null,h),v.return=m,v;case Er:return h=tl(h,m.mode,v),h.return=m,h;case mn:var S=h._init;return p(m,S(h._payload),v)}if(To(h)||yo(h))return h=ir(h,m.mode,v,null),h.return=m,h;Us(m,h)}return null}function d(m,h,v,S){var N=h!==null?h.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return N!==null?null:a(m,h,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case _s:return v.key===N?c(m,h,v,S):null;case Er:return v.key===N?u(m,h,v,S):null;case mn:return N=v._init,d(m,h,N(v._payload),S)}if(To(v)||yo(v))return N!==null?null:f(m,h,v,S,null);Us(m,v)}return null}function x(m,h,v,S,N){if(typeof S=="string"&&S!==""||typeof S=="number")return m=m.get(v)||null,a(h,m,""+S,N);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case _s:return m=m.get(S.key===null?v:S.key)||null,c(h,m,S,N);case Er:return m=m.get(S.key===null?v:S.key)||null,u(h,m,S,N);case mn:var b=S._init;return x(m,h,v,b(S._payload),N)}if(To(S)||yo(S))return m=m.get(v)||null,f(h,m,S,N,null);Us(h,S)}return null}function w(m,h,v,S){for(var N=null,b=null,k=h,j=h=0,R=null;k!==null&&j<v.length;j++){k.index>j?(R=k,k=null):R=k.sibling;var O=d(m,k,v[j],S);if(O===null){k===null&&(k=R);break}e&&k&&O.alternate===null&&t(m,k),h=s(O,h,j),b===null?N=O:b.sibling=O,b=O,k=R}if(j===v.length)return n(m,k),ue&&Yn(m,j),N;if(k===null){for(;j<v.length;j++)k=p(m,v[j],S),k!==null&&(h=s(k,h,j),b===null?N=k:b.sibling=k,b=k);return ue&&Yn(m,j),N}for(k=r(m,k);j<v.length;j++)R=x(k,m,j,v[j],S),R!==null&&(e&&R.alternate!==null&&k.delete(R.key===null?j:R.key),h=s(R,h,j),b===null?N=R:b.sibling=R,b=R);return e&&k.forEach(function($){return t(m,$)}),ue&&Yn(m,j),N}function y(m,h,v,S){var N=yo(v);if(typeof N!="function")throw Error(_(150));if(v=N.call(v),v==null)throw Error(_(151));for(var b=N=null,k=h,j=h=0,R=null,O=v.next();k!==null&&!O.done;j++,O=v.next()){k.index>j?(R=k,k=null):R=k.sibling;var $=d(m,k,O.value,S);if($===null){k===null&&(k=R);break}e&&k&&$.alternate===null&&t(m,k),h=s($,h,j),b===null?N=$:b.sibling=$,b=$,k=R}if(O.done)return n(m,k),ue&&Yn(m,j),N;if(k===null){for(;!O.done;j++,O=v.next())O=p(m,O.value,S),O!==null&&(h=s(O,h,j),b===null?N=O:b.sibling=O,b=O);return ue&&Yn(m,j),N}for(k=r(m,k);!O.done;j++,O=v.next())O=x(k,m,j,O.value,S),O!==null&&(e&&O.alternate!==null&&k.delete(O.key===null?j:O.key),h=s(O,h,j),b===null?N=O:b.sibling=O,b=O);return e&&k.forEach(function(T){return t(m,T)}),ue&&Yn(m,j),N}function C(m,h,v,S){if(typeof v=="object"&&v!==null&&v.type===br&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case _s:e:{for(var N=v.key,b=h;b!==null;){if(b.key===N){if(N=v.type,N===br){if(b.tag===7){n(m,b.sibling),h=o(b,v.props.children),h.return=m,m=h;break e}}else if(b.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===mn&&Ad(N)===b.type){n(m,b.sibling),h=o(b,v.props),h.ref=No(m,b,v),h.return=m,m=h;break e}n(m,b);break}else t(m,b);b=b.sibling}v.type===br?(h=ir(v.props.children,m.mode,S,v.key),h.return=m,m=h):(S=ci(v.type,v.key,v.props,null,m.mode,S),S.ref=No(m,h,v),S.return=m,m=S)}return i(m);case Er:e:{for(b=v.key;h!==null;){if(h.key===b)if(h.tag===4&&h.stateNode.containerInfo===v.containerInfo&&h.stateNode.implementation===v.implementation){n(m,h.sibling),h=o(h,v.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=tl(v,m.mode,S),h.return=m,m=h}return i(m);case mn:return b=v._init,C(m,h,b(v._payload),S)}if(To(v))return w(m,h,v,S);if(yo(v))return y(m,h,v,S);Us(m,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,h!==null&&h.tag===6?(n(m,h.sibling),h=o(h,v),h.return=m,m=h):(n(m,h),h=el(v,m.mode,S),h.return=m,m=h),i(m)):n(m,h)}return C}var no=hh(!0),mh=hh(!1),ji=Gn(null),ki=null,Ar=null,Gc=null;function Qc(){Gc=Ar=ki=null}function Kc(e){var t=ji.current;le(ji),e._currentValue=t}function Bl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function $r(e,t){ki=e,Gc=Ar=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ke=!0),e.firstContext=null)}function mt(e){var t=e._currentValue;if(Gc!==e)if(e={context:e,memoizedValue:t,next:null},Ar===null){if(ki===null)throw Error(_(308));Ar=e,ki.dependencies={lanes:0,firstContext:e}}else Ar=Ar.next=e;return t}var Jn=null;function qc(e){Jn===null?Jn=[e]:Jn.push(e)}function gh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,qc(t)):(n.next=o.next,o.next=n),t.interleaved=n,Jt(e,r)}function Jt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gn=!1;function Yc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function vh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Yt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function _n(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Y&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Jt(e,n)}return o=r.interleaved,o===null?(t.next=t,qc(r)):(t.next=o.next,o.next=t),r.interleaved=t,Jt(e,n)}function ri(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Lc(e,n)}}function Md(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Pi(e,t,n,r){var o=e.updateQueue;gn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var c=a,u=c.next;c.next=null,i===null?s=u:i.next=u,i=c;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==i&&(a===null?f.firstBaseUpdate=u:a.next=u,f.lastBaseUpdate=c))}if(s!==null){var p=o.baseState;i=0,f=u=c=null,a=s;do{var d=a.lane,x=a.eventTime;if((r&d)===d){f!==null&&(f=f.next={eventTime:x,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,y=a;switch(d=t,x=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){p=w.call(x,p,d);break e}p=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,d=typeof w=="function"?w.call(x,p,d):w,d==null)break e;p=he({},p,d);break e;case 2:gn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else x={eventTime:x,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(u=f=x,c=p):f=f.next=x,i|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(f===null&&(c=p),o.baseState=c,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);dr|=i,e.lanes=i,e.memoizedState=p}}function Ld(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(_(191,o));o.call(r)}}}var ws={},$t=Gn(ws),Jo=Gn(ws),es=Gn(ws);function er(e){if(e===ws)throw Error(_(174));return e}function Xc(e,t){switch(oe(es,t),oe(Jo,e),oe($t,ws),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Cl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Cl(t,e)}le($t),oe($t,t)}function ro(){le($t),le(Jo),le(es)}function yh(e){er(es.current);var t=er($t.current),n=Cl(t,e.type);t!==n&&(oe(Jo,e),oe($t,n))}function Zc(e){Jo.current===e&&(le($t),le(Jo))}var fe=Gn(0);function Ti(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ka=[];function Jc(){for(var e=0;e<Ka.length;e++)Ka[e]._workInProgressVersionPrimary=null;Ka.length=0}var oi=rn.ReactCurrentDispatcher,qa=rn.ReactCurrentBatchConfig,ur=0,pe=null,Ce=null,Ne=null,Ri=!1,Do=!1,ts=0,M0=0;function Me(){throw Error(_(321))}function eu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!kt(e[n],t[n]))return!1;return!0}function tu(e,t,n,r,o,s){if(ur=s,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oi.current=e===null||e.memoizedState===null?F0:z0,e=n(r,o),Do){s=0;do{if(Do=!1,ts=0,25<=s)throw Error(_(301));s+=1,Ne=Ce=null,t.updateQueue=null,oi.current=$0,e=n(r,o)}while(Do)}if(oi.current=_i,t=Ce!==null&&Ce.next!==null,ur=0,Ne=Ce=pe=null,Ri=!1,t)throw Error(_(300));return e}function nu(){var e=ts!==0;return ts=0,e}function Mt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ne===null?pe.memoizedState=Ne=e:Ne=Ne.next=e,Ne}function gt(){if(Ce===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Ne===null?pe.memoizedState:Ne.next;if(t!==null)Ne=t,Ce=e;else{if(e===null)throw Error(_(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Ne===null?pe.memoizedState=Ne=e:Ne=Ne.next=e}return Ne}function ns(e,t){return typeof t=="function"?t(e):t}function Ya(e){var t=gt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=Ce,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=i=null,c=null,u=s;do{var f=u.lane;if((ur&f)===f)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(a=c=p,i=r):c=c.next=p,pe.lanes|=f,dr|=f}u=u.next}while(u!==null&&u!==s);c===null?i=r:c.next=a,kt(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,pe.lanes|=s,dr|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Xa(e){var t=gt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);kt(s,t.memoizedState)||(Ke=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function xh(){}function wh(e,t){var n=pe,r=gt(),o=t(),s=!kt(r.memoizedState,o);if(s&&(r.memoizedState=o,Ke=!0),r=r.queue,ru(Nh.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||Ne!==null&&Ne.memoizedState.tag&1){if(n.flags|=2048,rs(9,Sh.bind(null,n,r,o,t),void 0,null),Ee===null)throw Error(_(349));ur&30||Ch(n,t,o)}return o}function Ch(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Sh(e,t,n,r){t.value=n,t.getSnapshot=r,Eh(t)&&bh(e)}function Nh(e,t,n){return n(function(){Eh(t)&&bh(e)})}function Eh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!kt(e,n)}catch{return!0}}function bh(e){var t=Jt(e,1);t!==null&&jt(t,e,1,-1)}function Id(e){var t=Mt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ns,lastRenderedState:e},t.queue=e,e=e.dispatch=D0.bind(null,pe,e),[t.memoizedState,e]}function rs(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function jh(){return gt().memoizedState}function si(e,t,n,r){var o=Mt();pe.flags|=e,o.memoizedState=rs(1|t,n,void 0,r===void 0?null:r)}function ea(e,t,n,r){var o=gt();r=r===void 0?null:r;var s=void 0;if(Ce!==null){var i=Ce.memoizedState;if(s=i.destroy,r!==null&&eu(r,i.deps)){o.memoizedState=rs(t,n,s,r);return}}pe.flags|=e,o.memoizedState=rs(1|t,n,s,r)}function Dd(e,t){return si(8390656,8,e,t)}function ru(e,t){return ea(2048,8,e,t)}function kh(e,t){return ea(4,2,e,t)}function Ph(e,t){return ea(4,4,e,t)}function Th(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Rh(e,t,n){return n=n!=null?n.concat([e]):null,ea(4,4,Th.bind(null,t,e),n)}function ou(){}function _h(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&eu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Oh(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&eu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ah(e,t,n){return ur&21?(kt(n,t)||(n=Fp(),pe.lanes|=n,dr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=n)}function L0(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=qa.transition;qa.transition={};try{e(!1),t()}finally{ne=n,qa.transition=r}}function Mh(){return gt().memoizedState}function I0(e,t,n){var r=An(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Lh(e))Ih(t,n);else if(n=gh(e,t,n,r),n!==null){var o=Ve();jt(n,e,r,o),Dh(n,t,r)}}function D0(e,t,n){var r=An(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Lh(e))Ih(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,a=s(i,n);if(o.hasEagerState=!0,o.eagerState=a,kt(a,i)){var c=t.interleaved;c===null?(o.next=o,qc(t)):(o.next=c.next,c.next=o),t.interleaved=o;return}}catch{}finally{}n=gh(e,t,o,r),n!==null&&(o=Ve(),jt(n,e,r,o),Dh(n,t,r))}}function Lh(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Ih(e,t){Do=Ri=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Dh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Lc(e,n)}}var _i={readContext:mt,useCallback:Me,useContext:Me,useEffect:Me,useImperativeHandle:Me,useInsertionEffect:Me,useLayoutEffect:Me,useMemo:Me,useReducer:Me,useRef:Me,useState:Me,useDebugValue:Me,useDeferredValue:Me,useTransition:Me,useMutableSource:Me,useSyncExternalStore:Me,useId:Me,unstable_isNewReconciler:!1},F0={readContext:mt,useCallback:function(e,t){return Mt().memoizedState=[e,t===void 0?null:t],e},useContext:mt,useEffect:Dd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,si(4194308,4,Th.bind(null,t,e),n)},useLayoutEffect:function(e,t){return si(4194308,4,e,t)},useInsertionEffect:function(e,t){return si(4,2,e,t)},useMemo:function(e,t){var n=Mt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Mt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=I0.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Mt();return e={current:e},t.memoizedState=e},useState:Id,useDebugValue:ou,useDeferredValue:function(e){return Mt().memoizedState=e},useTransition:function(){var e=Id(!1),t=e[0];return e=L0.bind(null,e[1]),Mt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,o=Mt();if(ue){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),Ee===null)throw Error(_(349));ur&30||Ch(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,Dd(Nh.bind(null,r,s,e),[e]),r.flags|=2048,rs(9,Sh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Mt(),t=Ee.identifierPrefix;if(ue){var n=qt,r=Kt;n=(r&~(1<<32-bt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ts++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=M0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},z0={readContext:mt,useCallback:_h,useContext:mt,useEffect:ru,useImperativeHandle:Rh,useInsertionEffect:kh,useLayoutEffect:Ph,useMemo:Oh,useReducer:Ya,useRef:jh,useState:function(){return Ya(ns)},useDebugValue:ou,useDeferredValue:function(e){var t=gt();return Ah(t,Ce.memoizedState,e)},useTransition:function(){var e=Ya(ns)[0],t=gt().memoizedState;return[e,t]},useMutableSource:xh,useSyncExternalStore:wh,useId:Mh,unstable_isNewReconciler:!1},$0={readContext:mt,useCallback:_h,useContext:mt,useEffect:ru,useImperativeHandle:Rh,useInsertionEffect:kh,useLayoutEffect:Ph,useMemo:Oh,useReducer:Xa,useRef:jh,useState:function(){return Xa(ns)},useDebugValue:ou,useDeferredValue:function(e){var t=gt();return Ce===null?t.memoizedState=e:Ah(t,Ce.memoizedState,e)},useTransition:function(){var e=Xa(ns)[0],t=gt().memoizedState;return[e,t]},useMutableSource:xh,useSyncExternalStore:wh,useId:Mh,unstable_isNewReconciler:!1};function wt(e,t){if(e&&e.defaultProps){t=he({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ul(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:he({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ta={isMounted:function(e){return(e=e._reactInternals)?gr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=An(e),s=Yt(r,o);s.payload=t,n!=null&&(s.callback=n),t=_n(e,s,o),t!==null&&(jt(t,e,o,r),ri(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=An(e),s=Yt(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=_n(e,s,o),t!==null&&(jt(t,e,o,r),ri(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ve(),r=An(e),o=Yt(n,r);o.tag=2,t!=null&&(o.callback=t),t=_n(e,o,r),t!==null&&(jt(t,e,r,n),ri(t,e,r))}};function Fd(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!qo(n,r)||!qo(o,s):!0}function Fh(e,t,n){var r=!1,o=Fn,s=t.contextType;return typeof s=="object"&&s!==null?s=mt(s):(o=Ye(t)?lr:Fe.current,r=t.contextTypes,s=(r=r!=null)?eo(e,o):Fn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ta,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function zd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ta.enqueueReplaceState(t,t.state,null)}function Vl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Yc(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=mt(s):(s=Ye(t)?lr:Fe.current,o.context=eo(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Ul(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ta.enqueueReplaceState(o,o.state,null),Pi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function oo(e,t){try{var n="",r=t;do n+=hy(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function Za(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Hl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var B0=typeof WeakMap=="function"?WeakMap:Map;function zh(e,t,n){n=Yt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ai||(Ai=!0,ec=r),Hl(e,t)},n}function $h(e,t,n){n=Yt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Hl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Hl(e,t),typeof r!="function"&&(On===null?On=new Set([this]):On.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function $d(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new B0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=tx.bind(null,e,t,n),t.then(e,e))}function Bd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ud(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Yt(-1,1),t.tag=2,_n(n,t,1))),n.lanes|=1),e)}var U0=rn.ReactCurrentOwner,Ke=!1;function $e(e,t,n,r){t.child=e===null?mh(t,null,n,r):no(t,e.child,n,r)}function Vd(e,t,n,r,o){n=n.render;var s=t.ref;return $r(t,o),r=tu(e,t,n,r,s,o),n=nu(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,en(e,t,o)):(ue&&n&&Vc(t),t.flags|=1,$e(e,t,r,o),t.child)}function Hd(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!fu(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Bh(e,t,s,r,o)):(e=ci(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:qo,n(i,r)&&e.ref===t.ref)return en(e,t,o)}return t.flags|=1,e=Mn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Bh(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(qo(s,r)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Ke=!0);else return t.lanes=e.lanes,en(e,t,o)}return Wl(e,t,n,r,o)}function Uh(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},oe(Lr,tt),tt|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,oe(Lr,tt),tt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,oe(Lr,tt),tt|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,oe(Lr,tt),tt|=r;return $e(e,t,o,n),t.child}function Vh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Wl(e,t,n,r,o){var s=Ye(n)?lr:Fe.current;return s=eo(t,s),$r(t,o),n=tu(e,t,n,r,s,o),r=nu(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,en(e,t,o)):(ue&&r&&Vc(t),t.flags|=1,$e(e,t,n,o),t.child)}function Wd(e,t,n,r,o){if(Ye(n)){var s=!0;Ni(t)}else s=!1;if($r(t,o),t.stateNode===null)ii(e,t),Fh(t,n,r),Vl(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=mt(u):(u=Ye(n)?lr:Fe.current,u=eo(t,u));var f=n.getDerivedStateFromProps,p=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";p||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||c!==u)&&zd(t,i,r,u),gn=!1;var d=t.memoizedState;i.state=d,Pi(t,r,i,o),c=t.memoizedState,a!==r||d!==c||qe.current||gn?(typeof f=="function"&&(Ul(t,n,f,r),c=t.memoizedState),(a=gn||Fd(t,n,a,r,d,c,u))?(p||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),i.props=r,i.state=c,i.context=u,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,vh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:wt(t.type,a),i.props=u,p=t.pendingProps,d=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=mt(c):(c=Ye(n)?lr:Fe.current,c=eo(t,c));var x=n.getDerivedStateFromProps;(f=typeof x=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==p||d!==c)&&zd(t,i,r,c),gn=!1,d=t.memoizedState,i.state=d,Pi(t,r,i,o);var w=t.memoizedState;a!==p||d!==w||qe.current||gn?(typeof x=="function"&&(Ul(t,n,x,r),w=t.memoizedState),(u=gn||Fd(t,n,u,r,d,w,c)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,w,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,w,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),i.props=r,i.state=w,i.context=c,r=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Gl(e,t,n,r,s,o)}function Gl(e,t,n,r,o,s){Vh(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&Rd(t,n,!1),en(e,t,s);r=t.stateNode,U0.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=no(t,e.child,null,s),t.child=no(t,null,a,s)):$e(e,t,a,s),t.memoizedState=r.state,o&&Rd(t,n,!0),t.child}function Hh(e){var t=e.stateNode;t.pendingContext?Td(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Td(e,t.context,!1),Xc(e,t.containerInfo)}function Gd(e,t,n,r,o){return to(),Wc(o),t.flags|=256,$e(e,t,n,r),t.child}var Ql={dehydrated:null,treeContext:null,retryLane:0};function Kl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Wh(e,t,n){var r=t.pendingProps,o=fe.current,s=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),oe(fe,o&1),e===null)return $l(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=oa(i,r,0,null),e=ir(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Kl(n),t.memoizedState=Ql,e):su(t,i));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return V0(e,t,i,r,a,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,a=o.sibling;var c={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Mn(o,c),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=Mn(a,s):(s=ir(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?Kl(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=Ql,r}return s=e.child,e=s.sibling,r=Mn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function su(e,t){return t=oa({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Vs(e,t,n,r){return r!==null&&Wc(r),no(t,e.child,null,n),e=su(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function V0(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=Za(Error(_(422))),Vs(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=oa({mode:"visible",children:r.children},o,0,null),s=ir(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&no(t,e.child,null,i),t.child.memoizedState=Kl(i),t.memoizedState=Ql,s);if(!(t.mode&1))return Vs(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(_(419)),r=Za(s,r,void 0),Vs(e,t,i,r)}if(a=(i&e.childLanes)!==0,Ke||a){if(r=Ee,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,Jt(e,o),jt(r,e,o,-1))}return du(),r=Za(Error(_(421))),Vs(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=nx.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,rt=Rn(o.nextSibling),ot=t,ue=!0,Et=null,e!==null&&(dt[ft++]=Kt,dt[ft++]=qt,dt[ft++]=cr,Kt=e.id,qt=e.overflow,cr=t),t=su(t,r.children),t.flags|=4096,t)}function Qd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Bl(e.return,t,n)}function Ja(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Gh(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if($e(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Qd(e,n,t);else if(e.tag===19)Qd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(oe(fe,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ti(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ja(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ti(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ja(t,!0,n,null,s);break;case"together":Ja(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ii(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function en(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),dr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Mn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Mn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function H0(e,t,n){switch(t.tag){case 3:Hh(t),to();break;case 5:yh(t);break;case 1:Ye(t.type)&&Ni(t);break;case 4:Xc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;oe(ji,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(oe(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?Wh(e,t,n):(oe(fe,fe.current&1),e=en(e,t,n),e!==null?e.sibling:null);oe(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Gh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),oe(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,Uh(e,t,n)}return en(e,t,n)}var Qh,ql,Kh,qh;Qh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ql=function(){};Kh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,er($t.current);var s=null;switch(n){case"input":o=vl(e,o),r=vl(e,r),s=[];break;case"select":o=he({},o,{value:void 0}),r=he({},r,{value:void 0}),s=[];break;case"textarea":o=wl(e,o),r=wl(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ci)}Sl(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Uo.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var c=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&c!==a&&(c!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(s||(s=[]),s.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(s=s||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(s=s||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Uo.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&ae("scroll",e),s||a===c||(s=[])):(s=s||[]).push(u,c))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};qh=function(e,t,n,r){n!==r&&(t.flags|=4)};function Eo(e,t){if(!ue)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function W0(e,t,n){var r=t.pendingProps;switch(Hc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Ye(t.type)&&Si(),Le(t),null;case 3:return r=t.stateNode,ro(),le(qe),le(Fe),Jc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Bs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Et!==null&&(rc(Et),Et=null))),ql(e,t),Le(t),null;case 5:Zc(t);var o=er(es.current);if(n=t.type,e!==null&&t.stateNode!=null)Kh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return Le(t),null}if(e=er($t.current),Bs(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Ft]=t,r[Zo]=s,e=(t.mode&1)!==0,n){case"dialog":ae("cancel",r),ae("close",r);break;case"iframe":case"object":case"embed":ae("load",r);break;case"video":case"audio":for(o=0;o<_o.length;o++)ae(_o[o],r);break;case"source":ae("error",r);break;case"img":case"image":case"link":ae("error",r),ae("load",r);break;case"details":ae("toggle",r);break;case"input":nd(r,s),ae("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},ae("invalid",r);break;case"textarea":od(r,s),ae("invalid",r)}Sl(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&$s(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&$s(r.textContent,a,e),o=["children",""+a]):Uo.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&ae("scroll",r)}switch(n){case"input":Os(r),rd(r,s,!0);break;case"textarea":Os(r),sd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Ci)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Np(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Ft]=t,e[Zo]=r,Qh(e,t,!1,!1),t.stateNode=e;e:{switch(i=Nl(n,r),n){case"dialog":ae("cancel",e),ae("close",e),o=r;break;case"iframe":case"object":case"embed":ae("load",e),o=r;break;case"video":case"audio":for(o=0;o<_o.length;o++)ae(_o[o],e);o=r;break;case"source":ae("error",e),o=r;break;case"img":case"image":case"link":ae("error",e),ae("load",e),o=r;break;case"details":ae("toggle",e),o=r;break;case"input":nd(e,r),o=vl(e,r),ae("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=he({},r,{value:void 0}),ae("invalid",e);break;case"textarea":od(e,r),o=wl(e,r),ae("invalid",e);break;default:o=r}Sl(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var c=a[s];s==="style"?jp(e,c):s==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Ep(e,c)):s==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Vo(e,c):typeof c=="number"&&Vo(e,""+c):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Uo.hasOwnProperty(s)?c!=null&&s==="onScroll"&&ae("scroll",e):c!=null&&Tc(e,s,c,i))}switch(n){case"input":Os(e),rd(e,r,!1);break;case"textarea":Os(e),sd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Dn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Ir(e,!!r.multiple,s,!1):r.defaultValue!=null&&Ir(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ci)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Le(t),null;case 6:if(e&&t.stateNode!=null)qh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=er(es.current),er($t.current),Bs(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ft]=t,(s=r.nodeValue!==n)&&(e=ot,e!==null))switch(e.tag){case 3:$s(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&$s(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ft]=t,t.stateNode=r}return Le(t),null;case 13:if(le(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ue&&rt!==null&&t.mode&1&&!(t.flags&128))ph(),to(),t.flags|=98560,s=!1;else if(s=Bs(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(_(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(_(317));s[Ft]=t}else to(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Le(t),s=!1}else Et!==null&&(rc(Et),Et=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?Se===0&&(Se=3):du())),t.updateQueue!==null&&(t.flags|=4),Le(t),null);case 4:return ro(),ql(e,t),e===null&&Yo(t.stateNode.containerInfo),Le(t),null;case 10:return Kc(t.type._context),Le(t),null;case 17:return Ye(t.type)&&Si(),Le(t),null;case 19:if(le(fe),s=t.memoizedState,s===null)return Le(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)Eo(s,!1);else{if(Se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Ti(e),i!==null){for(t.flags|=128,Eo(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return oe(fe,fe.current&1|2),t.child}e=e.sibling}s.tail!==null&&ye()>so&&(t.flags|=128,r=!0,Eo(s,!1),t.lanes=4194304)}else{if(!r)if(e=Ti(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Eo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!ue)return Le(t),null}else 2*ye()-s.renderingStartTime>so&&n!==1073741824&&(t.flags|=128,r=!0,Eo(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ye(),t.sibling=null,n=fe.current,oe(fe,r?n&1|2:n&1),t):(Le(t),null);case 22:case 23:return uu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?tt&1073741824&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function G0(e,t){switch(Hc(t),t.tag){case 1:return Ye(t.type)&&Si(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ro(),le(qe),le(Fe),Jc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Zc(t),null;case 13:if(le(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));to()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(fe),null;case 4:return ro(),null;case 10:return Kc(t.type._context),null;case 22:case 23:return uu(),null;case 24:return null;default:return null}}var Hs=!1,De=!1,Q0=typeof WeakSet=="function"?WeakSet:Set,I=null;function Mr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ve(e,t,r)}else n.current=null}function Yl(e,t,n){try{n()}catch(r){ve(e,t,r)}}var Kd=!1;function K0(e,t){if(Al=yi,e=eh(),Uc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,a=-1,c=-1,u=0,f=0,p=e,d=null;t:for(;;){for(var x;p!==n||o!==0&&p.nodeType!==3||(a=i+o),p!==s||r!==0&&p.nodeType!==3||(c=i+r),p.nodeType===3&&(i+=p.nodeValue.length),(x=p.firstChild)!==null;)d=p,p=x;for(;;){if(p===e)break t;if(d===n&&++u===o&&(a=i),d===s&&++f===r&&(c=i),(x=p.nextSibling)!==null)break;p=d,d=p.parentNode}p=x}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ml={focusedElem:e,selectionRange:n},yi=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,C=w.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:wt(t.type,y),C);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(S){ve(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return w=Kd,Kd=!1,w}function Fo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&Yl(t,n,s)}o=o.next}while(o!==r)}}function na(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Yh(e){var t=e.alternate;t!==null&&(e.alternate=null,Yh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ft],delete t[Zo],delete t[Dl],delete t[R0],delete t[_0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Xh(e){return e.tag===5||e.tag===3||e.tag===4}function qd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Xh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Zl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ci));else if(r!==4&&(e=e.child,e!==null))for(Zl(e,t,n),e=e.sibling;e!==null;)Zl(e,t,n),e=e.sibling}function Jl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Jl(e,t,n),e=e.sibling;e!==null;)Jl(e,t,n),e=e.sibling}var je=null,Nt=!1;function un(e,t,n){for(n=n.child;n!==null;)Zh(e,t,n),n=n.sibling}function Zh(e,t,n){if(zt&&typeof zt.onCommitFiberUnmount=="function")try{zt.onCommitFiberUnmount(Ki,n)}catch{}switch(n.tag){case 5:De||Mr(n,t);case 6:var r=je,o=Nt;je=null,un(e,t,n),je=r,Nt=o,je!==null&&(Nt?(e=je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):je.removeChild(n.stateNode));break;case 18:je!==null&&(Nt?(e=je,n=n.stateNode,e.nodeType===8?Ga(e.parentNode,n):e.nodeType===1&&Ga(e,n),Qo(e)):Ga(je,n.stateNode));break;case 4:r=je,o=Nt,je=n.stateNode.containerInfo,Nt=!0,un(e,t,n),je=r,Nt=o;break;case 0:case 11:case 14:case 15:if(!De&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&Yl(n,t,i),o=o.next}while(o!==r)}un(e,t,n);break;case 1:if(!De&&(Mr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ve(n,t,a)}un(e,t,n);break;case 21:un(e,t,n);break;case 22:n.mode&1?(De=(r=De)||n.memoizedState!==null,un(e,t,n),De=r):un(e,t,n);break;default:un(e,t,n)}}function Yd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Q0),t.forEach(function(r){var o=rx.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function xt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:je=a.stateNode,Nt=!1;break e;case 3:je=a.stateNode.containerInfo,Nt=!0;break e;case 4:je=a.stateNode.containerInfo,Nt=!0;break e}a=a.return}if(je===null)throw Error(_(160));Zh(s,i,o),je=null,Nt=!1;var c=o.alternate;c!==null&&(c.return=null),o.return=null}catch(u){ve(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Jh(t,e),t=t.sibling}function Jh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(xt(t,e),At(e),r&4){try{Fo(3,e,e.return),na(3,e)}catch(y){ve(e,e.return,y)}try{Fo(5,e,e.return)}catch(y){ve(e,e.return,y)}}break;case 1:xt(t,e),At(e),r&512&&n!==null&&Mr(n,n.return);break;case 5:if(xt(t,e),At(e),r&512&&n!==null&&Mr(n,n.return),e.flags&32){var o=e.stateNode;try{Vo(o,"")}catch(y){ve(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Cp(o,s),Nl(a,i);var u=Nl(a,s);for(i=0;i<c.length;i+=2){var f=c[i],p=c[i+1];f==="style"?jp(o,p):f==="dangerouslySetInnerHTML"?Ep(o,p):f==="children"?Vo(o,p):Tc(o,f,p,u)}switch(a){case"input":yl(o,s);break;case"textarea":Sp(o,s);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var x=s.value;x!=null?Ir(o,!!s.multiple,x,!1):d!==!!s.multiple&&(s.defaultValue!=null?Ir(o,!!s.multiple,s.defaultValue,!0):Ir(o,!!s.multiple,s.multiple?[]:"",!1))}o[Zo]=s}catch(y){ve(e,e.return,y)}}break;case 6:if(xt(t,e),At(e),r&4){if(e.stateNode===null)throw Error(_(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(y){ve(e,e.return,y)}}break;case 3:if(xt(t,e),At(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Qo(t.containerInfo)}catch(y){ve(e,e.return,y)}break;case 4:xt(t,e),At(e);break;case 13:xt(t,e),At(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(lu=ye())),r&4&&Yd(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(De=(u=De)||f,xt(t,e),De=u):xt(t,e),At(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(I=e,f=e.child;f!==null;){for(p=I=f;I!==null;){switch(d=I,x=d.child,d.tag){case 0:case 11:case 14:case 15:Fo(4,d,d.return);break;case 1:Mr(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){ve(r,n,y)}}break;case 5:Mr(d,d.return);break;case 22:if(d.memoizedState!==null){Zd(p);continue}}x!==null?(x.return=d,I=x):Zd(p)}f=f.sibling}e:for(f=null,p=e;;){if(p.tag===5){if(f===null){f=p;try{o=p.stateNode,u?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=p.stateNode,c=p.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=bp("display",i))}catch(y){ve(e,e.return,y)}}}else if(p.tag===6){if(f===null)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(y){ve(e,e.return,y)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;f===p&&(f=null),p=p.return}f===p&&(f=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:xt(t,e),At(e),r&4&&Yd(e);break;case 21:break;default:xt(t,e),At(e)}}function At(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Xh(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Vo(o,""),r.flags&=-33);var s=qd(e);Jl(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,a=qd(e);Zl(e,a,i);break;default:throw Error(_(161))}}catch(c){ve(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function q0(e,t,n){I=e,em(e)}function em(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||Hs;if(!i){var a=o.alternate,c=a!==null&&a.memoizedState!==null||De;a=Hs;var u=De;if(Hs=i,(De=c)&&!u)for(I=o;I!==null;)i=I,c=i.child,i.tag===22&&i.memoizedState!==null?Jd(o):c!==null?(c.return=i,I=c):Jd(o);for(;s!==null;)I=s,em(s),s=s.sibling;I=o,Hs=a,De=u}Xd(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,I=s):Xd(e)}}function Xd(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:De||na(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!De)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:wt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Ld(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ld(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var p=f.dehydrated;p!==null&&Qo(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}De||t.flags&512&&Xl(t)}catch(d){ve(t,t.return,d)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Zd(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function Jd(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{na(4,t)}catch(c){ve(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(c){ve(t,o,c)}}var s=t.return;try{Xl(t)}catch(c){ve(t,s,c)}break;case 5:var i=t.return;try{Xl(t)}catch(c){ve(t,i,c)}}}catch(c){ve(t,t.return,c)}if(t===e){I=null;break}var a=t.sibling;if(a!==null){a.return=t.return,I=a;break}I=t.return}}var Y0=Math.ceil,Oi=rn.ReactCurrentDispatcher,iu=rn.ReactCurrentOwner,ht=rn.ReactCurrentBatchConfig,Y=0,Ee=null,xe=null,_e=0,tt=0,Lr=Gn(0),Se=0,os=null,dr=0,ra=0,au=0,zo=null,Qe=null,lu=0,so=1/0,Gt=null,Ai=!1,ec=null,On=null,Ws=!1,bn=null,Mi=0,$o=0,tc=null,ai=-1,li=0;function Ve(){return Y&6?ye():ai!==-1?ai:ai=ye()}function An(e){return e.mode&1?Y&2&&_e!==0?_e&-_e:A0.transition!==null?(li===0&&(li=Fp()),li):(e=ne,e!==0||(e=window.event,e=e===void 0?16:Wp(e.type)),e):1}function jt(e,t,n,r){if(50<$o)throw $o=0,tc=null,Error(_(185));vs(e,n,r),(!(Y&2)||e!==Ee)&&(e===Ee&&(!(Y&2)&&(ra|=n),Se===4&&yn(e,_e)),Xe(e,r),n===1&&Y===0&&!(t.mode&1)&&(so=ye()+500,Ji&&Qn()))}function Xe(e,t){var n=e.callbackNode;Ay(e,t);var r=vi(e,e===Ee?_e:0);if(r===0)n!==null&&ld(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ld(n),t===1)e.tag===0?O0(ef.bind(null,e)):uh(ef.bind(null,e)),P0(function(){!(Y&6)&&Qn()}),n=null;else{switch(zp(r)){case 1:n=Mc;break;case 4:n=Ip;break;case 16:n=gi;break;case 536870912:n=Dp;break;default:n=gi}n=lm(n,tm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function tm(e,t){if(ai=-1,li=0,Y&6)throw Error(_(327));var n=e.callbackNode;if(Br()&&e.callbackNode!==n)return null;var r=vi(e,e===Ee?_e:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Li(e,r);else{t=r;var o=Y;Y|=2;var s=rm();(Ee!==e||_e!==t)&&(Gt=null,so=ye()+500,sr(e,t));do try{J0();break}catch(a){nm(e,a)}while(!0);Qc(),Oi.current=s,Y=o,xe!==null?t=0:(Ee=null,_e=0,t=Se)}if(t!==0){if(t===2&&(o=Pl(e),o!==0&&(r=o,t=nc(e,o))),t===1)throw n=os,sr(e,0),yn(e,r),Xe(e,ye()),n;if(t===6)yn(e,r);else{if(o=e.current.alternate,!(r&30)&&!X0(o)&&(t=Li(e,r),t===2&&(s=Pl(e),s!==0&&(r=s,t=nc(e,s))),t===1))throw n=os,sr(e,0),yn(e,r),Xe(e,ye()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:Xn(e,Qe,Gt);break;case 3:if(yn(e,r),(r&130023424)===r&&(t=lu+500-ye(),10<t)){if(vi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ve(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Il(Xn.bind(null,e,Qe,Gt),t);break}Xn(e,Qe,Gt);break;case 4:if(yn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-bt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=ye()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Y0(r/1960))-r,10<r){e.timeoutHandle=Il(Xn.bind(null,e,Qe,Gt),r);break}Xn(e,Qe,Gt);break;case 5:Xn(e,Qe,Gt);break;default:throw Error(_(329))}}}return Xe(e,ye()),e.callbackNode===n?tm.bind(null,e):null}function nc(e,t){var n=zo;return e.current.memoizedState.isDehydrated&&(sr(e,t).flags|=256),e=Li(e,t),e!==2&&(t=Qe,Qe=n,t!==null&&rc(t)),e}function rc(e){Qe===null?Qe=e:Qe.push.apply(Qe,e)}function X0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!kt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yn(e,t){for(t&=~au,t&=~ra,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-bt(t),r=1<<n;e[n]=-1,t&=~r}}function ef(e){if(Y&6)throw Error(_(327));Br();var t=vi(e,0);if(!(t&1))return Xe(e,ye()),null;var n=Li(e,t);if(e.tag!==0&&n===2){var r=Pl(e);r!==0&&(t=r,n=nc(e,r))}if(n===1)throw n=os,sr(e,0),yn(e,t),Xe(e,ye()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Xn(e,Qe,Gt),Xe(e,ye()),null}function cu(e,t){var n=Y;Y|=1;try{return e(t)}finally{Y=n,Y===0&&(so=ye()+500,Ji&&Qn())}}function fr(e){bn!==null&&bn.tag===0&&!(Y&6)&&Br();var t=Y;Y|=1;var n=ht.transition,r=ne;try{if(ht.transition=null,ne=1,e)return e()}finally{ne=r,ht.transition=n,Y=t,!(Y&6)&&Qn()}}function uu(){tt=Lr.current,le(Lr)}function sr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,k0(n)),xe!==null)for(n=xe.return;n!==null;){var r=n;switch(Hc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Si();break;case 3:ro(),le(qe),le(Fe),Jc();break;case 5:Zc(r);break;case 4:ro();break;case 13:le(fe);break;case 19:le(fe);break;case 10:Kc(r.type._context);break;case 22:case 23:uu()}n=n.return}if(Ee=e,xe=e=Mn(e.current,null),_e=tt=t,Se=0,os=null,au=ra=dr=0,Qe=zo=null,Jn!==null){for(t=0;t<Jn.length;t++)if(n=Jn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}Jn=null}return e}function nm(e,t){do{var n=xe;try{if(Qc(),oi.current=_i,Ri){for(var r=pe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ri=!1}if(ur=0,Ne=Ce=pe=null,Do=!1,ts=0,iu.current=null,n===null||n.return===null){Se=1,os=t,xe=null;break}e:{var s=e,i=n.return,a=n,c=t;if(t=_e,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,f=a,p=f.tag;if(!(f.mode&1)&&(p===0||p===11||p===15)){var d=f.alternate;d?(f.updateQueue=d.updateQueue,f.memoizedState=d.memoizedState,f.lanes=d.lanes):(f.updateQueue=null,f.memoizedState=null)}var x=Bd(i);if(x!==null){x.flags&=-257,Ud(x,i,a,s,t),x.mode&1&&$d(s,u,t),t=x,c=u;var w=t.updateQueue;if(w===null){var y=new Set;y.add(c),t.updateQueue=y}else w.add(c);break e}else{if(!(t&1)){$d(s,u,t),du();break e}c=Error(_(426))}}else if(ue&&a.mode&1){var C=Bd(i);if(C!==null){!(C.flags&65536)&&(C.flags|=256),Ud(C,i,a,s,t),Wc(oo(c,a));break e}}s=c=oo(c,a),Se!==4&&(Se=2),zo===null?zo=[s]:zo.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var m=zh(s,c,t);Md(s,m);break e;case 1:a=c;var h=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(On===null||!On.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=$h(s,a,t);Md(s,S);break e}}s=s.return}while(s!==null)}sm(n)}catch(N){t=N,xe===n&&n!==null&&(xe=n=n.return);continue}break}while(!0)}function rm(){var e=Oi.current;return Oi.current=_i,e===null?_i:e}function du(){(Se===0||Se===3||Se===2)&&(Se=4),Ee===null||!(dr&268435455)&&!(ra&268435455)||yn(Ee,_e)}function Li(e,t){var n=Y;Y|=2;var r=rm();(Ee!==e||_e!==t)&&(Gt=null,sr(e,t));do try{Z0();break}catch(o){nm(e,o)}while(!0);if(Qc(),Y=n,Oi.current=r,xe!==null)throw Error(_(261));return Ee=null,_e=0,Se}function Z0(){for(;xe!==null;)om(xe)}function J0(){for(;xe!==null&&!Ey();)om(xe)}function om(e){var t=am(e.alternate,e,tt);e.memoizedProps=e.pendingProps,t===null?sm(e):xe=t,iu.current=null}function sm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=G0(n,t),n!==null){n.flags&=32767,xe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Se=6,xe=null;return}}else if(n=W0(n,t,tt),n!==null){xe=n;return}if(t=t.sibling,t!==null){xe=t;return}xe=t=e}while(t!==null);Se===0&&(Se=5)}function Xn(e,t,n){var r=ne,o=ht.transition;try{ht.transition=null,ne=1,ex(e,t,n,r)}finally{ht.transition=o,ne=r}return null}function ex(e,t,n,r){do Br();while(bn!==null);if(Y&6)throw Error(_(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(My(e,s),e===Ee&&(xe=Ee=null,_e=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ws||(Ws=!0,lm(gi,function(){return Br(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=ht.transition,ht.transition=null;var i=ne;ne=1;var a=Y;Y|=4,iu.current=null,K0(e,n),Jh(n,e),w0(Ml),yi=!!Al,Ml=Al=null,e.current=n,q0(n),by(),Y=a,ne=i,ht.transition=s}else e.current=n;if(Ws&&(Ws=!1,bn=e,Mi=o),s=e.pendingLanes,s===0&&(On=null),Py(n.stateNode),Xe(e,ye()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ai)throw Ai=!1,e=ec,ec=null,e;return Mi&1&&e.tag!==0&&Br(),s=e.pendingLanes,s&1?e===tc?$o++:($o=0,tc=e):$o=0,Qn(),null}function Br(){if(bn!==null){var e=zp(Mi),t=ht.transition,n=ne;try{if(ht.transition=null,ne=16>e?16:e,bn===null)var r=!1;else{if(e=bn,bn=null,Mi=0,Y&6)throw Error(_(331));var o=Y;for(Y|=4,I=e.current;I!==null;){var s=I,i=s.child;if(I.flags&16){var a=s.deletions;if(a!==null){for(var c=0;c<a.length;c++){var u=a[c];for(I=u;I!==null;){var f=I;switch(f.tag){case 0:case 11:case 15:Fo(8,f,s)}var p=f.child;if(p!==null)p.return=f,I=p;else for(;I!==null;){f=I;var d=f.sibling,x=f.return;if(Yh(f),f===u){I=null;break}if(d!==null){d.return=x,I=d;break}I=x}}}var w=s.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var C=y.sibling;y.sibling=null,y=C}while(y!==null)}}I=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,I=i;else e:for(;I!==null;){if(s=I,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Fo(9,s,s.return)}var m=s.sibling;if(m!==null){m.return=s.return,I=m;break e}I=s.return}}var h=e.current;for(I=h;I!==null;){i=I;var v=i.child;if(i.subtreeFlags&2064&&v!==null)v.return=i,I=v;else e:for(i=h;I!==null;){if(a=I,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:na(9,a)}}catch(N){ve(a,a.return,N)}if(a===i){I=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,I=S;break e}I=a.return}}if(Y=o,Qn(),zt&&typeof zt.onPostCommitFiberRoot=="function")try{zt.onPostCommitFiberRoot(Ki,e)}catch{}r=!0}return r}finally{ne=n,ht.transition=t}}return!1}function tf(e,t,n){t=oo(n,t),t=zh(e,t,1),e=_n(e,t,1),t=Ve(),e!==null&&(vs(e,1,t),Xe(e,t))}function ve(e,t,n){if(e.tag===3)tf(e,e,n);else for(;t!==null;){if(t.tag===3){tf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(On===null||!On.has(r))){e=oo(n,e),e=$h(t,e,1),t=_n(t,e,1),e=Ve(),t!==null&&(vs(t,1,e),Xe(t,e));break}}t=t.return}}function tx(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&n,Ee===e&&(_e&n)===n&&(Se===4||Se===3&&(_e&130023424)===_e&&500>ye()-lu?sr(e,0):au|=n),Xe(e,t)}function im(e,t){t===0&&(e.mode&1?(t=Ls,Ls<<=1,!(Ls&130023424)&&(Ls=4194304)):t=1);var n=Ve();e=Jt(e,t),e!==null&&(vs(e,t,n),Xe(e,n))}function nx(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),im(e,n)}function rx(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),im(e,n)}var am;am=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||qe.current)Ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ke=!1,H0(e,t,n);Ke=!!(e.flags&131072)}else Ke=!1,ue&&t.flags&1048576&&dh(t,bi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ii(e,t),e=t.pendingProps;var o=eo(t,Fe.current);$r(t,n),o=tu(null,t,r,e,o,n);var s=nu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ye(r)?(s=!0,Ni(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Yc(t),o.updater=ta,t.stateNode=o,o._reactInternals=t,Vl(t,r,e,n),t=Gl(null,t,r,!0,s,n)):(t.tag=0,ue&&s&&Vc(t),$e(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ii(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=sx(r),e=wt(r,e),o){case 0:t=Wl(null,t,r,e,n);break e;case 1:t=Wd(null,t,r,e,n);break e;case 11:t=Vd(null,t,r,e,n);break e;case 14:t=Hd(null,t,r,wt(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),Wl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),Wd(e,t,r,o,n);case 3:e:{if(Hh(t),e===null)throw Error(_(387));r=t.pendingProps,s=t.memoizedState,o=s.element,vh(e,t),Pi(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=oo(Error(_(423)),t),t=Gd(e,t,r,n,o);break e}else if(r!==o){o=oo(Error(_(424)),t),t=Gd(e,t,r,n,o);break e}else for(rt=Rn(t.stateNode.containerInfo.firstChild),ot=t,ue=!0,Et=null,n=mh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(to(),r===o){t=en(e,t,n);break e}$e(e,t,r,n)}t=t.child}return t;case 5:return yh(t),e===null&&$l(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,Ll(r,o)?i=null:s!==null&&Ll(r,s)&&(t.flags|=32),Vh(e,t),$e(e,t,i,n),t.child;case 6:return e===null&&$l(t),null;case 13:return Wh(e,t,n);case 4:return Xc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=no(t,null,r,n):$e(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),Vd(e,t,r,o,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,oe(ji,r._currentValue),r._currentValue=i,s!==null)if(kt(s.value,i)){if(s.children===o.children&&!qe.current){t=en(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){i=s.child;for(var c=a.firstContext;c!==null;){if(c.context===r){if(s.tag===1){c=Yt(-1,n&-n),c.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?c.next=c:(c.next=f.next,f.next=c),u.pending=c}}s.lanes|=n,c=s.alternate,c!==null&&(c.lanes|=n),Bl(s.return,n,t),a.lanes|=n;break}c=c.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(_(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Bl(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}$e(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,$r(t,n),o=mt(o),r=r(o),t.flags|=1,$e(e,t,r,n),t.child;case 14:return r=t.type,o=wt(r,t.pendingProps),o=wt(r.type,o),Hd(e,t,r,o,n);case 15:return Bh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),ii(e,t),t.tag=1,Ye(r)?(e=!0,Ni(t)):e=!1,$r(t,n),Fh(t,r,o),Vl(t,r,o,n),Gl(null,t,r,!0,e,n);case 19:return Gh(e,t,n);case 22:return Uh(e,t,n)}throw Error(_(156,t.tag))};function lm(e,t){return Lp(e,t)}function ox(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pt(e,t,n,r){return new ox(e,t,n,r)}function fu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sx(e){if(typeof e=="function")return fu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===_c)return 11;if(e===Oc)return 14}return 2}function Mn(e,t){var n=e.alternate;return n===null?(n=pt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ci(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")fu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case br:return ir(n.children,o,s,t);case Rc:i=8,o|=8;break;case pl:return e=pt(12,n,t,o|2),e.elementType=pl,e.lanes=s,e;case hl:return e=pt(13,n,t,o),e.elementType=hl,e.lanes=s,e;case ml:return e=pt(19,n,t,o),e.elementType=ml,e.lanes=s,e;case yp:return oa(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case gp:i=10;break e;case vp:i=9;break e;case _c:i=11;break e;case Oc:i=14;break e;case mn:i=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=pt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function ir(e,t,n,r){return e=pt(7,e,r,t),e.lanes=n,e}function oa(e,t,n,r){return e=pt(22,e,r,t),e.elementType=yp,e.lanes=n,e.stateNode={isHidden:!1},e}function el(e,t,n){return e=pt(6,e,null,t),e.lanes=n,e}function tl(e,t,n){return t=pt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ix(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=La(0),this.expirationTimes=La(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=La(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function pu(e,t,n,r,o,s,i,a,c){return e=new ix(e,t,n,a,c),t===1?(t=1,s===!0&&(t|=8)):t=0,s=pt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Yc(s),e}function ax(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Er,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function cm(e){if(!e)return Fn;e=e._reactInternals;e:{if(gr(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(Ye(n))return ch(e,n,t)}return t}function um(e,t,n,r,o,s,i,a,c){return e=pu(n,r,!0,e,o,s,i,a,c),e.context=cm(null),n=e.current,r=Ve(),o=An(n),s=Yt(r,o),s.callback=t??null,_n(n,s,o),e.current.lanes=o,vs(e,o,r),Xe(e,r),e}function sa(e,t,n,r){var o=t.current,s=Ve(),i=An(o);return n=cm(n),t.context===null?t.context=n:t.pendingContext=n,t=Yt(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=_n(o,t,i),e!==null&&(jt(e,o,i,s),ri(e,o,i)),i}function Ii(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function hu(e,t){nf(e,t),(e=e.alternate)&&nf(e,t)}function lx(){return null}var dm=typeof reportError=="function"?reportError:function(e){console.error(e)};function mu(e){this._internalRoot=e}ia.prototype.render=mu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));sa(e,t,null,null)};ia.prototype.unmount=mu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;fr(function(){sa(null,e,null,null)}),t[Zt]=null}};function ia(e){this._internalRoot=e}ia.prototype.unstable_scheduleHydration=function(e){if(e){var t=Up();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vn.length&&t!==0&&t<vn[n].priority;n++);vn.splice(n,0,e),n===0&&Hp(e)}};function gu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function aa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rf(){}function cx(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var u=Ii(i);s.call(u)}}var i=um(t,r,e,0,null,!1,!1,"",rf);return e._reactRootContainer=i,e[Zt]=i.current,Yo(e.nodeType===8?e.parentNode:e),fr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Ii(c);a.call(u)}}var c=pu(e,0,!1,null,null,!1,!1,"",rf);return e._reactRootContainer=c,e[Zt]=c.current,Yo(e.nodeType===8?e.parentNode:e),fr(function(){sa(t,c,n,r)}),c}function la(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var a=o;o=function(){var c=Ii(i);a.call(c)}}sa(t,i,e,o)}else i=cx(n,t,e,o,r);return Ii(i)}$p=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ro(t.pendingLanes);n!==0&&(Lc(t,n|1),Xe(t,ye()),!(Y&6)&&(so=ye()+500,Qn()))}break;case 13:fr(function(){var r=Jt(e,1);if(r!==null){var o=Ve();jt(r,e,1,o)}}),hu(e,1)}};Ic=function(e){if(e.tag===13){var t=Jt(e,134217728);if(t!==null){var n=Ve();jt(t,e,134217728,n)}hu(e,134217728)}};Bp=function(e){if(e.tag===13){var t=An(e),n=Jt(e,t);if(n!==null){var r=Ve();jt(n,e,t,r)}hu(e,t)}};Up=function(){return ne};Vp=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};bl=function(e,t,n){switch(t){case"input":if(yl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Zi(r);if(!o)throw Error(_(90));wp(r),yl(r,o)}}}break;case"textarea":Sp(e,n);break;case"select":t=n.value,t!=null&&Ir(e,!!n.multiple,t,!1)}};Tp=cu;Rp=fr;var ux={usingClientEntryPoint:!1,Events:[xs,Tr,Zi,kp,Pp,cu]},bo={findFiberByHostInstance:Zn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},dx={bundleType:bo.bundleType,version:bo.version,rendererPackageName:bo.rendererPackageName,rendererConfig:bo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:rn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ap(e),e===null?null:e.stateNode},findFiberByHostInstance:bo.findFiberByHostInstance||lx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Gs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Gs.isDisabled&&Gs.supportsFiber)try{Ki=Gs.inject(dx),zt=Gs}catch{}}lt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ux;lt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!gu(t))throw Error(_(200));return ax(e,t,null,n)};lt.createRoot=function(e,t){if(!gu(e))throw Error(_(299));var n=!1,r="",o=dm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=pu(e,1,!1,null,null,n,!1,r,o),e[Zt]=t.current,Yo(e.nodeType===8?e.parentNode:e),new mu(t)};lt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Ap(t),e=e===null?null:e.stateNode,e};lt.flushSync=function(e){return fr(e)};lt.hydrate=function(e,t,n){if(!aa(t))throw Error(_(200));return la(null,e,t,!0,n)};lt.hydrateRoot=function(e,t,n){if(!gu(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=dm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=um(t,null,e,1,n??null,o,!1,s,i),e[Zt]=t.current,Yo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ia(t)};lt.render=function(e,t,n){if(!aa(t))throw Error(_(200));return la(null,e,t,!1,n)};lt.unmountComponentAtNode=function(e){if(!aa(e))throw Error(_(40));return e._reactRootContainer?(fr(function(){la(null,null,e,!1,function(){e._reactRootContainer=null,e[Zt]=null})}),!0):!1};lt.unstable_batchedUpdates=cu;lt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!aa(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return la(e,t,n,!1,r)};lt.version="18.3.1-next-f1338f8080-20240426";function fm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(fm)}catch(e){console.error(e)}}fm(),fp.exports=lt;var Cs=fp.exports;const pm=Jf(Cs);var hm,of=Cs;hm=of.createRoot,of.hydrateRoot;const fx=1,px=1e6;let nl=0;function hx(){return nl=(nl+1)%Number.MAX_SAFE_INTEGER,nl.toString()}const rl=new Map,sf=e=>{if(rl.has(e))return;const t=setTimeout(()=>{rl.delete(e),Bo({type:"REMOVE_TOAST",toastId:e})},px);rl.set(e,t)},mx=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,fx)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?sf(n):e.toasts.forEach(r=>{sf(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},ui=[];let di={toasts:[]};function Bo(e){di=mx(di,e),ui.forEach(t=>{t(di)})}function gx({...e}){const t=hx(),n=o=>Bo({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Bo({type:"DISMISS_TOAST",toastId:t});return Bo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function vx(){const[e,t]=g.useState(di);return g.useEffect(()=>(ui.push(t),()=>{const n=ui.indexOf(t);n>-1&&ui.splice(n,1)}),[e]),{...e,toast:gx,dismiss:n=>Bo({type:"DISMISS_TOAST",toastId:n})}}function ee(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function yx(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function mm(...e){return t=>e.forEach(n=>yx(n,t))}function at(...e){return g.useCallback(mm(...e),e)}function xx(e,t=[]){let n=[];function r(s,i){const a=g.createContext(i),c=n.length;n=[...n,i];function u(p){const{scope:d,children:x,...w}=p,y=(d==null?void 0:d[e][c])||a,C=g.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:C,children:x})}function f(p,d){const x=(d==null?void 0:d[e][c])||a,w=g.useContext(x);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,f]}const o=()=>{const s=n.map(i=>g.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,wx(o,...t)]}function wx(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var ss=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),s=o.find(Cx);if(s){const i=s.props.children,a=o.map(c=>c===s?g.Children.count(i)>1?g.Children.only(null):g.isValidElement(i)?i.props.children:null:c);return l.jsx(oc,{...r,ref:t,children:g.isValidElement(i)?g.cloneElement(i,void 0,a):null})}return l.jsx(oc,{...r,ref:t,children:n})});ss.displayName="Slot";var oc=g.forwardRef((e,t)=>{const{children:n,...r}=e;if(g.isValidElement(n)){const o=Nx(n);return g.cloneElement(n,{...Sx(r,n.props),ref:t?mm(t,o):o})}return g.Children.count(n)>1?g.Children.only(null):null});oc.displayName="SlotClone";var gm=({children:e})=>l.jsx(l.Fragment,{children:e});function Cx(e){return g.isValidElement(e)&&e.type===gm}function Sx(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Nx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function vm(e){const t=e+"CollectionProvider",[n,r]=xx(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=x=>{const{scope:w,children:y}=x,C=M.useRef(null),m=M.useRef(new Map).current;return l.jsx(o,{scope:w,itemMap:m,collectionRef:C,children:y})};i.displayName=t;const a=e+"CollectionSlot",c=M.forwardRef((x,w)=>{const{scope:y,children:C}=x,m=s(a,y),h=at(w,m.collectionRef);return l.jsx(ss,{ref:h,children:C})});c.displayName=a;const u=e+"CollectionItemSlot",f="data-radix-collection-item",p=M.forwardRef((x,w)=>{const{scope:y,children:C,...m}=x,h=M.useRef(null),v=at(w,h),S=s(u,y);return M.useEffect(()=>(S.itemMap.set(h,{ref:h,...m}),()=>void S.itemMap.delete(h))),l.jsx(ss,{[f]:"",ref:v,children:C})});p.displayName=u;function d(x){const w=s(e+"CollectionConsumer",x);return M.useCallback(()=>{const C=w.collectionRef.current;if(!C)return[];const m=Array.from(C.querySelectorAll(`[${f}]`));return Array.from(w.itemMap.values()).sort((S,N)=>m.indexOf(S.ref.current)-m.indexOf(N.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:i,Slot:c,ItemSlot:p},d,r]}function ca(e,t=[]){let n=[];function r(s,i){const a=g.createContext(i),c=n.length;n=[...n,i];const u=p=>{var m;const{scope:d,children:x,...w}=p,y=((m=d==null?void 0:d[e])==null?void 0:m[c])||a,C=g.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:C,children:x})};u.displayName=s+"Provider";function f(p,d){var y;const x=((y=d==null?void 0:d[e])==null?void 0:y[c])||a,w=g.useContext(x);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[u,f]}const o=()=>{const s=n.map(i=>g.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,Ex(o,...t)]}function Ex(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var bx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ce=bx.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:s,...i}=r,a=s?ss:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(a,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ym(e,t){e&&Cs.flushSync(()=>e.dispatchEvent(t))}function Pt(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function jx(e,t=globalThis==null?void 0:globalThis.document){const n=Pt(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var kx="DismissableLayer",sc="dismissableLayer.update",Px="dismissableLayer.pointerDownOutside",Tx="dismissableLayer.focusOutside",af,xm=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),vu=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...c}=e,u=g.useContext(xm),[f,p]=g.useState(null),d=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,x]=g.useState({}),w=at(t,k=>p(k)),y=Array.from(u.layers),[C]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),m=y.indexOf(C),h=f?y.indexOf(f):-1,v=u.layersWithOutsidePointerEventsDisabled.size>0,S=h>=m,N=_x(k=>{const j=k.target,R=[...u.branches].some(O=>O.contains(j));!S||R||(o==null||o(k),i==null||i(k),k.defaultPrevented||a==null||a())},d),b=Ox(k=>{const j=k.target;[...u.branches].some(O=>O.contains(j))||(s==null||s(k),i==null||i(k),k.defaultPrevented||a==null||a())},d);return jx(k=>{h===u.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&a&&(k.preventDefault(),a()))},d),g.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(af=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),lf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=af)}},[f,d,n,u]),g.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),lf())},[f,u]),g.useEffect(()=>{const k=()=>x({});return document.addEventListener(sc,k),()=>document.removeEventListener(sc,k)},[]),l.jsx(ce.div,{...c,ref:w,style:{pointerEvents:v?S?"auto":"none":void 0,...e.style},onFocusCapture:ee(e.onFocusCapture,b.onFocusCapture),onBlurCapture:ee(e.onBlurCapture,b.onBlurCapture),onPointerDownCapture:ee(e.onPointerDownCapture,N.onPointerDownCapture)})});vu.displayName=kx;var Rx="DismissableLayerBranch",wm=g.forwardRef((e,t)=>{const n=g.useContext(xm),r=g.useRef(null),o=at(t,r);return g.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),l.jsx(ce.div,{...e,ref:o})});wm.displayName=Rx;function _x(e,t=globalThis==null?void 0:globalThis.document){const n=Pt(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let c=function(){Cm(Px,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=c,t.addEventListener("click",o.current,{once:!0})):c()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Ox(e,t=globalThis==null?void 0:globalThis.document){const n=Pt(e),r=g.useRef(!1);return g.useEffect(()=>{const o=s=>{s.target&&!r.current&&Cm(Tx,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function lf(){const e=new CustomEvent(sc);document.dispatchEvent(e)}function Cm(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ym(o,s):o.dispatchEvent(s)}var Ax=vu,Mx=wm,zn=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},Lx="Portal",Sm=g.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=g.useState(!1);zn(()=>s(!0),[]);const i=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?pm.createPortal(l.jsx(ce.div,{...r,ref:t}),i):null});Sm.displayName=Lx;function Ix(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var Ss=e=>{const{present:t,children:n}=e,r=Dx(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),s=at(r.ref,Fx(o));return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:s}):null};Ss.displayName="Presence";function Dx(e){const[t,n]=g.useState(),r=g.useRef({}),o=g.useRef(e),s=g.useRef("none"),i=e?"mounted":"unmounted",[a,c]=Ix(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=Qs(r.current);s.current=a==="mounted"?u:"none"},[a]),zn(()=>{const u=r.current,f=o.current;if(f!==e){const d=s.current,x=Qs(u);e?c("MOUNT"):x==="none"||(u==null?void 0:u.display)==="none"?c("UNMOUNT"):c(f&&d!==x?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,c]),zn(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,p=x=>{const y=Qs(r.current).includes(x.animationName);if(x.target===t&&y&&(c("ANIMATION_END"),!o.current)){const C=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=C)})}},d=x=>{x.target===t&&(s.current=Qs(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:g.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Qs(e){return(e==null?void 0:e.animationName)||"none"}function Fx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function ua({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=zx({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,a=Pt(n),c=g.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&a(p)}else o(u)},[s,e,o,a]);return[i,c]}function zx({defaultProp:e,onChange:t}){const n=g.useState(e),[r]=n,o=g.useRef(r),s=Pt(t);return g.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var $x="VisuallyHidden",da=g.forwardRef((e,t)=>l.jsx(ce.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));da.displayName=$x;var Bx=da,yu="ToastProvider",[xu,Ux,Vx]=vm("Toast"),[Nm,LN]=ca("Toast",[Vx]),[Hx,fa]=Nm(yu),Em=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[a,c]=g.useState(null),[u,f]=g.useState(0),p=g.useRef(!1),d=g.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${yu}\`. Expected non-empty \`string\`.`),l.jsx(xu.Provider,{scope:t,children:l.jsx(Hx,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:a,onViewportChange:c,onToastAdd:g.useCallback(()=>f(x=>x+1),[]),onToastRemove:g.useCallback(()=>f(x=>x-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:d,children:i})})};Em.displayName=yu;var bm="ToastViewport",Wx=["F8"],ic="toast.viewportPause",ac="toast.viewportResume",jm=g.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=Wx,label:o="Notifications ({hotkey})",...s}=e,i=fa(bm,n),a=Ux(n),c=g.useRef(null),u=g.useRef(null),f=g.useRef(null),p=g.useRef(null),d=at(t,p,i.onViewportChange),x=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=i.toastCount>0;g.useEffect(()=>{const C=m=>{var v;r.length!==0&&r.every(S=>m[S]||m.code===S)&&((v=p.current)==null||v.focus())};return document.addEventListener("keydown",C),()=>document.removeEventListener("keydown",C)},[r]),g.useEffect(()=>{const C=c.current,m=p.current;if(w&&C&&m){const h=()=>{if(!i.isClosePausedRef.current){const b=new CustomEvent(ic);m.dispatchEvent(b),i.isClosePausedRef.current=!0}},v=()=>{if(i.isClosePausedRef.current){const b=new CustomEvent(ac);m.dispatchEvent(b),i.isClosePausedRef.current=!1}},S=b=>{!C.contains(b.relatedTarget)&&v()},N=()=>{C.contains(document.activeElement)||v()};return C.addEventListener("focusin",h),C.addEventListener("focusout",S),C.addEventListener("pointermove",h),C.addEventListener("pointerleave",N),window.addEventListener("blur",h),window.addEventListener("focus",v),()=>{C.removeEventListener("focusin",h),C.removeEventListener("focusout",S),C.removeEventListener("pointermove",h),C.removeEventListener("pointerleave",N),window.removeEventListener("blur",h),window.removeEventListener("focus",v)}}},[w,i.isClosePausedRef]);const y=g.useCallback(({tabbingDirection:C})=>{const h=a().map(v=>{const S=v.ref.current,N=[S,...o1(S)];return C==="forwards"?N:N.reverse()});return(C==="forwards"?h.reverse():h).flat()},[a]);return g.useEffect(()=>{const C=p.current;if(C){const m=h=>{var N,b,k;const v=h.altKey||h.ctrlKey||h.metaKey;if(h.key==="Tab"&&!v){const j=document.activeElement,R=h.shiftKey;if(h.target===C&&R){(N=u.current)==null||N.focus();return}const T=y({tabbingDirection:R?"backwards":"forwards"}),F=T.findIndex(L=>L===j);ol(T.slice(F+1))?h.preventDefault():R?(b=u.current)==null||b.focus():(k=f.current)==null||k.focus()}};return C.addEventListener("keydown",m),()=>C.removeEventListener("keydown",m)}},[a,y]),l.jsxs(Mx,{ref:c,role:"region","aria-label":o.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&l.jsx(lc,{ref:u,onFocusFromOutsideViewport:()=>{const C=y({tabbingDirection:"forwards"});ol(C)}}),l.jsx(xu.Slot,{scope:n,children:l.jsx(ce.ol,{tabIndex:-1,...s,ref:d})}),w&&l.jsx(lc,{ref:f,onFocusFromOutsideViewport:()=>{const C=y({tabbingDirection:"backwards"});ol(C)}})]})});jm.displayName=bm;var km="ToastFocusProxy",lc=g.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=fa(km,n);return l.jsx(da,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var u;const a=i.relatedTarget;!((u=s.viewport)!=null&&u.contains(a))&&r()}})});lc.displayName=km;var pa="Toast",Gx="toast.swipeStart",Qx="toast.swipeMove",Kx="toast.swipeCancel",qx="toast.swipeEnd",Pm=g.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[a=!0,c]=ua({prop:r,defaultProp:o,onChange:s});return l.jsx(Ss,{present:n||a,children:l.jsx(Zx,{open:a,...i,ref:t,onClose:()=>c(!1),onPause:Pt(e.onPause),onResume:Pt(e.onResume),onSwipeStart:ee(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ee(e.onSwipeMove,u=>{const{x:f,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:ee(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ee(e.onSwipeEnd,u=>{const{x:f,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),c(!1)})})})});Pm.displayName=pa;var[Yx,Xx]=Nm(pa,{onClose(){}}),Zx=g.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:a,onPause:c,onResume:u,onSwipeStart:f,onSwipeMove:p,onSwipeCancel:d,onSwipeEnd:x,...w}=e,y=fa(pa,n),[C,m]=g.useState(null),h=at(t,L=>m(L)),v=g.useRef(null),S=g.useRef(null),N=o||y.duration,b=g.useRef(0),k=g.useRef(N),j=g.useRef(0),{onToastAdd:R,onToastRemove:O}=y,$=Pt(()=>{var V;(C==null?void 0:C.contains(document.activeElement))&&((V=y.viewport)==null||V.focus()),i()}),T=g.useCallback(L=>{!L||L===1/0||(window.clearTimeout(j.current),b.current=new Date().getTime(),j.current=window.setTimeout($,L))},[$]);g.useEffect(()=>{const L=y.viewport;if(L){const V=()=>{T(k.current),u==null||u()},B=()=>{const Q=new Date().getTime()-b.current;k.current=k.current-Q,window.clearTimeout(j.current),c==null||c()};return L.addEventListener(ic,B),L.addEventListener(ac,V),()=>{L.removeEventListener(ic,B),L.removeEventListener(ac,V)}}},[y.viewport,N,c,u,T]),g.useEffect(()=>{s&&!y.isClosePausedRef.current&&T(N)},[s,N,y.isClosePausedRef,T]),g.useEffect(()=>(R(),()=>O()),[R,O]);const F=g.useMemo(()=>C?Lm(C):null,[C]);return y.viewport?l.jsxs(l.Fragment,{children:[F&&l.jsx(Jx,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:F}),l.jsx(Yx,{scope:n,onClose:$,children:Cs.createPortal(l.jsx(xu.ItemSlot,{scope:n,children:l.jsx(Ax,{asChild:!0,onEscapeKeyDown:ee(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||$(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx(ce.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":y.swipeDirection,...w,ref:h,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ee(e.onKeyDown,L=>{L.key==="Escape"&&(a==null||a(L.nativeEvent),L.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:ee(e.onPointerDown,L=>{L.button===0&&(v.current={x:L.clientX,y:L.clientY})}),onPointerMove:ee(e.onPointerMove,L=>{if(!v.current)return;const V=L.clientX-v.current.x,B=L.clientY-v.current.y,Q=!!S.current,E=["left","right"].includes(y.swipeDirection),A=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,z=E?A(0,V):0,D=E?0:A(0,B),U=L.pointerType==="touch"?10:2,q={x:z,y:D},de={originalEvent:L,delta:q};Q?(S.current=q,Ks(Qx,p,de,{discrete:!1})):cf(q,y.swipeDirection,U)?(S.current=q,Ks(Gx,f,de,{discrete:!1}),L.target.setPointerCapture(L.pointerId)):(Math.abs(V)>U||Math.abs(B)>U)&&(v.current=null)}),onPointerUp:ee(e.onPointerUp,L=>{const V=S.current,B=L.target;if(B.hasPointerCapture(L.pointerId)&&B.releasePointerCapture(L.pointerId),S.current=null,v.current=null,V){const Q=L.currentTarget,E={originalEvent:L,delta:V};cf(V,y.swipeDirection,y.swipeThreshold)?Ks(qx,x,E,{discrete:!0}):Ks(Kx,d,E,{discrete:!0}),Q.addEventListener("click",A=>A.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),Jx=e=>{const{__scopeToast:t,children:n,...r}=e,o=fa(pa,t),[s,i]=g.useState(!1),[a,c]=g.useState(!1);return n1(()=>i(!0)),g.useEffect(()=>{const u=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:l.jsx(Sm,{asChild:!0,children:l.jsx(da,{...r,children:s&&l.jsxs(l.Fragment,{children:[o.label," ",n]})})})},e1="ToastTitle",Tm=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(ce.div,{...r,ref:t})});Tm.displayName=e1;var t1="ToastDescription",Rm=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(ce.div,{...r,ref:t})});Rm.displayName=t1;var _m="ToastAction",Om=g.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?l.jsx(Mm,{altText:n,asChild:!0,children:l.jsx(wu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${_m}\`. Expected non-empty \`string\`.`),null)});Om.displayName=_m;var Am="ToastClose",wu=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=Xx(Am,n);return l.jsx(Mm,{asChild:!0,children:l.jsx(ce.button,{type:"button",...r,ref:t,onClick:ee(e.onClick,o.onClose)})})});wu.displayName=Am;var Mm=g.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return l.jsx(ce.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Lm(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),r1(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...Lm(r))}}),t}function Ks(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ym(o,s):o.dispatchEvent(s)}var cf=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function n1(e=()=>{}){const t=Pt(e);zn(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function r1(e){return e.nodeType===e.ELEMENT_NODE}function o1(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ol(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var s1=Em,Im=jm,Dm=Pm,Fm=Tm,zm=Rm,$m=Om,Bm=wu;function Um(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Um(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Vm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Um(e))&&(r&&(r+=" "),r+=t);return r}const uf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,df=Vm,ha=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return df(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],p=s==null?void 0:s[u];if(f===null)return null;const d=uf(f)||uf(p);return o[u][d]}),a=n&&Object.entries(n).reduce((u,f)=>{let[p,d]=f;return d===void 0||(u[p]=d),u},{}),c=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:p,className:d,...x}=f;return Object.entries(x).every(w=>{let[y,C]=w;return Array.isArray(C)?C.includes({...s,...a}[y]):{...s,...a}[y]===C})?[...u,p,d]:u},[]);return df(e,i,c,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Hm=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=g.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...a},c)=>g.createElement("svg",{ref:c,...a1,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Hm("lucide",o),...a},[...i.map(([u,f])=>g.createElement(u,f)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=(e,t)=>{const n=g.forwardRef(({className:r,...o},s)=>g.createElement(l1,{ref:s,iconNode:t,className:Hm(`lucide-${i1(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=Z("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=Z("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=Z("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d1=Z("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=Z("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=Z("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h1=Z("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Di=Z("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wm=Z("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ma=Z("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fi=Z("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const as=Z("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=Z("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g1=Z("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ff=Z("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sl=Z("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pf=Z("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=Z("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gm=Z("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qm=Z("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Km=Z("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y1=Z("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x1=Z("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hf=Z("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=Z("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C1=Z("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S1=Z("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N1=Z("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E1=Z("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=Z("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b1=Z("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j1=Z("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),Cu="-",k1=e=>{const t=T1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(Cu);return a[0]===""&&a.length!==1&&a.shift(),qm(a,t)||P1(i)},getConflictingClassGroupIds:(i,a)=>{const c=n[i]||[];return a&&r[i]?[...c,...r[i]]:c}}},qm=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?qm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(Cu);return(i=t.validators.find(({validator:a})=>a(s)))==null?void 0:i.classGroupId},mf=/^\[(.+)\]$/,P1=e=>{if(mf.test(e)){const t=mf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},T1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return _1(Object.entries(e.classGroups),n).forEach(([s,i])=>{cc(i,r,s,t)}),r},cc=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:gf(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(R1(o)){cc(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{cc(i,gf(t,s),n,r)})})},gf=(e,t)=>{let n=e;return t.split(Cu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},R1=e=>e.isThemeGetter,_1=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,a])=>[t+i,a])):s);return[n,o]}):e,O1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Ym="!",A1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=a=>{const c=[];let u=0,f=0,p;for(let C=0;C<a.length;C++){let m=a[C];if(u===0){if(m===o&&(r||a.slice(C,C+s)===t)){c.push(a.slice(f,C)),f=C+s;continue}if(m==="/"){p=C;continue}}m==="["?u++:m==="]"&&u--}const d=c.length===0?a:a.substring(f),x=d.startsWith(Ym),w=x?d.substring(1):d,y=p&&p>f?p-f:void 0;return{modifiers:c,hasImportantModifier:x,baseClassName:w,maybePostfixModifierPosition:y}};return n?a=>n({className:a,parseClassName:i}):i},M1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},L1=e=>({cache:O1(e.cacheSize),parseClassName:A1(e),...k1(e)}),I1=/\s+/,D1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(I1);let a="";for(let c=i.length-1;c>=0;c-=1){const u=i[c],{modifiers:f,hasImportantModifier:p,baseClassName:d,maybePostfixModifierPosition:x}=n(u);let w=!!x,y=r(w?d.substring(0,x):d);if(!y){if(!w){a=u+(a.length>0?" "+a:a);continue}if(y=r(d),!y){a=u+(a.length>0?" "+a:a);continue}w=!1}const C=M1(f).join(":"),m=p?C+Ym:C,h=m+y;if(s.includes(h))continue;s.push(h);const v=o(y,w);for(let S=0;S<v.length;++S){const N=v[S];s.push(m+N)}a=u+(a.length>0?" "+a:a)}return a};function F1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Xm(t))&&(r&&(r+=" "),r+=n);return r}const Xm=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Xm(e[r]))&&(n&&(n+=" "),n+=t);return n};function z1(e,...t){let n,r,o,s=i;function i(c){const u=t.reduce((f,p)=>p(f),e());return n=L1(u),r=n.cache.get,o=n.cache.set,s=a,a(c)}function a(c){const u=r(c);if(u)return u;const f=D1(c,n);return o(c,f),f}return function(){return s(F1.apply(null,arguments))}}const ie=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Zm=/^\[(?:([a-z-]+):)?(.+)\]$/i,$1=/^\d+\/\d+$/,B1=new Set(["px","full","screen"]),U1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,V1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,H1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,W1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,G1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ht=e=>Ur(e)||B1.has(e)||$1.test(e),dn=e=>fo(e,"length",ew),Ur=e=>!!e&&!Number.isNaN(Number(e)),il=e=>fo(e,"number",Ur),jo=e=>!!e&&Number.isInteger(Number(e)),Q1=e=>e.endsWith("%")&&Ur(e.slice(0,-1)),W=e=>Zm.test(e),fn=e=>U1.test(e),K1=new Set(["length","size","percentage"]),q1=e=>fo(e,K1,Jm),Y1=e=>fo(e,"position",Jm),X1=new Set(["image","url"]),Z1=e=>fo(e,X1,nw),J1=e=>fo(e,"",tw),ko=()=>!0,fo=(e,t,n)=>{const r=Zm.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},ew=e=>V1.test(e)&&!H1.test(e),Jm=()=>!1,tw=e=>W1.test(e),nw=e=>G1.test(e),rw=()=>{const e=ie("colors"),t=ie("spacing"),n=ie("blur"),r=ie("brightness"),o=ie("borderColor"),s=ie("borderRadius"),i=ie("borderSpacing"),a=ie("borderWidth"),c=ie("contrast"),u=ie("grayscale"),f=ie("hueRotate"),p=ie("invert"),d=ie("gap"),x=ie("gradientColorStops"),w=ie("gradientColorStopPositions"),y=ie("inset"),C=ie("margin"),m=ie("opacity"),h=ie("padding"),v=ie("saturate"),S=ie("scale"),N=ie("sepia"),b=ie("skew"),k=ie("space"),j=ie("translate"),R=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto",W,t],T=()=>[W,t],F=()=>["",Ht,dn],L=()=>["auto",Ur,W],V=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],A=()=>["","0",W],z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[Ur,W];return{cacheSize:500,separator:":",theme:{colors:[ko],spacing:[Ht,dn],blur:["none","",fn,W],brightness:D(),borderColor:[e],borderRadius:["none","","full",fn,W],borderSpacing:T(),borderWidth:F(),contrast:D(),grayscale:A(),hueRotate:D(),invert:A(),gap:T(),gradientColorStops:[e],gradientColorStopPositions:[Q1,dn],inset:$(),margin:$(),opacity:D(),padding:T(),saturate:D(),scale:D(),sepia:A(),skew:D(),space:T(),translate:T()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[fn]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...V(),W]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",jo,W]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",jo,W]}],"grid-cols":[{"grid-cols":[ko]}],"col-start-end":[{col:["auto",{span:["full",jo,W]},W]}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":[ko]}],"row-start-end":[{row:["auto",{span:[jo,W]},W]}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[C]}],mx:[{mx:[C]}],my:[{my:[C]}],ms:[{ms:[C]}],me:[{me:[C]}],mt:[{mt:[C]}],mr:[{mr:[C]}],mb:[{mb:[C]}],ml:[{ml:[C]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,t]}],"min-w":[{"min-w":[W,t,"min","max","fit"]}],"max-w":[{"max-w":[W,t,"none","full","min","max","fit","prose",{screen:[fn]},fn]}],h:[{h:[W,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,t,"auto","min","max","fit"]}],"font-size":[{text:["base",fn,dn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",il]}],"font-family":[{font:[ko]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",Ur,il]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ht,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ht,dn]}],"underline-offset":[{"underline-offset":["auto",Ht,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...V(),Y1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",q1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Z1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[x]}],"gradient-via":[{via:[x]}],"gradient-to":[{to:[x]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[Ht,W]}],"outline-w":[{outline:[Ht,dn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:F()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Ht,dn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",fn,J1]}],"shadow-color":[{shadow:[ko]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...Q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",fn,W]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[p]}],saturate:[{saturate:[v]}],sepia:[{sepia:[N]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[jo,W]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[b]}],"skew-y":[{"skew-y":[b]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ht,dn,il]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},ow=z1(rw);function me(...e){return ow(Vm(e))}const sw=s1,eg=g.forwardRef(({className:e,...t},n)=>l.jsx(Im,{ref:n,className:me("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));eg.displayName=Im.displayName;const iw=ha("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),tg=g.forwardRef(({className:e,variant:t,...n},r)=>l.jsx(Dm,{ref:r,className:me(iw({variant:t}),e),...n}));tg.displayName=Dm.displayName;const aw=g.forwardRef(({className:e,...t},n)=>l.jsx($m,{ref:n,className:me("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));aw.displayName=$m.displayName;const ng=g.forwardRef(({className:e,...t},n)=>l.jsx(Bm,{ref:n,className:me("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:l.jsx(E1,{className:"h-4 w-4"})}));ng.displayName=Bm.displayName;const rg=g.forwardRef(({className:e,...t},n)=>l.jsx(Fm,{ref:n,className:me("text-sm font-semibold",e),...t}));rg.displayName=Fm.displayName;const og=g.forwardRef(({className:e,...t},n)=>l.jsx(zm,{ref:n,className:me("text-sm opacity-90",e),...t}));og.displayName=zm.displayName;function lw(){const{toasts:e}=vx();return l.jsxs(sw,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return l.jsxs(tg,{...s,children:[l.jsxs("div",{className:"grid gap-1",children:[n&&l.jsx(rg,{children:n}),r&&l.jsx(og,{children:r})]}),o,l.jsx(ng,{})]},t)}),l.jsx(eg,{})]})}var vf=["light","dark"],cw="(prefers-color-scheme: dark)",uw=g.createContext(void 0),dw={setTheme:e=>{},themes:[]},fw=()=>{var e;return(e=g.useContext(uw))!=null?e:dw};g.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:s,value:i,attrs:a,nonce:c})=>{let u=s==="system",f=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${a.map(w=>`'${w}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,p=o?vf.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(w,y=!1,C=!0)=>{let m=i?i[w]:w,h=y?w+"|| ''":`'${m}'`,v="";return o&&C&&!y&&vf.includes(w)&&(v+=`d.style.colorScheme = '${w}';`),n==="class"?y||m?v+=`c.add(${h})`:v+="null":m&&(v+=`d[s](n,${h})`),v},x=e?`!function(){${f}${d(e)}}()`:r?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${cw}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}${u?"":"else{"+d(s,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}else{${d(s,!1,!1)};}${p}}catch(t){}}();`;return g.createElement("script",{nonce:c,dangerouslySetInnerHTML:{__html:x}})});var pw=e=>{switch(e){case"success":return gw;case"info":return yw;case"warning":return vw;case"error":return xw;default:return null}},hw=Array(12).fill(0),mw=({visible:e})=>M.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},M.createElement("div",{className:"sonner-spinner"},hw.map((t,n)=>M.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),gw=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),vw=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),yw=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),xw=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ww=()=>{let[e,t]=M.useState(document.hidden);return M.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},uc=1,Cw=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:uc++,s=this.toasts.find(a=>a.id===o),i=e.dismissible===void 0?!0:e.dismissible;return s?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:i,title:n}):a):this.addToast({title:n,...r,dismissible:i,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async s=>{if(Nw(s)&&!s.ok){o=!1;let i=typeof t.error=="function"?await t.error(`HTTP error! status: ${s.status}`):t.error,a=typeof t.description=="function"?await t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:n,type:"error",message:i,description:a})}else if(t.success!==void 0){o=!1;let i=typeof t.success=="function"?await t.success(s):t.success,a=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"success",message:i,description:a})}}).catch(async s=>{if(t.error!==void 0){o=!1;let i=typeof t.error=="function"?await t.error(s):t.error,a=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"error",message:i,description:a})}}).finally(()=>{var s;o&&(this.dismiss(n),n=void 0),(s=t.finally)==null||s.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||uc++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},et=new Cw,Sw=(e,t)=>{let n=(t==null?void 0:t.id)||uc++;return et.addToast({title:e,...t,id:n}),n},Nw=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Ew=Sw,bw=()=>et.toasts;Object.assign(Ew,{success:et.success,info:et.info,warning:et.warning,error:et.error,custom:et.custom,message:et.message,promise:et.promise,dismiss:et.dismiss,loading:et.loading},{getHistory:bw});function jw(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}jw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function qs(e){return e.label!==void 0}var kw=3,Pw="32px",Tw=4e3,Rw=356,_w=14,Ow=20,Aw=200;function Mw(...e){return e.filter(Boolean).join(" ")}var Lw=e=>{var t,n,r,o,s,i,a,c,u,f;let{invert:p,toast:d,unstyled:x,interacting:w,setHeights:y,visibleToasts:C,heights:m,index:h,toasts:v,expanded:S,removeToast:N,defaultRichColors:b,closeButton:k,style:j,cancelButtonStyle:R,actionButtonStyle:O,className:$="",descriptionClassName:T="",duration:F,position:L,gap:V,loadingIcon:B,expandByDefault:Q,classNames:E,icons:A,closeButtonAriaLabel:z="Close toast",pauseWhenPageIsHidden:D,cn:U}=e,[q,de]=M.useState(!1),[Ze,te]=M.useState(!1),[yt,on]=M.useState(!1),[sn,an]=M.useState(!1),[bs,yr]=M.useState(0),[qn,vo]=M.useState(0),js=M.useRef(null),ln=M.useRef(null),ba=h===0,ja=h+1<=C,be=d.type,xr=d.dismissible!==!1,_v=d.className||"",Ov=d.descriptionClassName||"",ks=M.useMemo(()=>m.findIndex(H=>H.toastId===d.id)||0,[m,d.id]),Av=M.useMemo(()=>{var H;return(H=d.closeButton)!=null?H:k},[d.closeButton,k]),Vu=M.useMemo(()=>d.duration||F||Tw,[d.duration,F]),ka=M.useRef(0),wr=M.useRef(0),Hu=M.useRef(0),Cr=M.useRef(null),[Wu,Mv]=L.split("-"),Gu=M.useMemo(()=>m.reduce((H,se,re)=>re>=ks?H:H+se.height,0),[m,ks]),Qu=ww(),Lv=d.invert||p,Pa=be==="loading";wr.current=M.useMemo(()=>ks*V+Gu,[ks,Gu]),M.useEffect(()=>{de(!0)},[]),M.useLayoutEffect(()=>{if(!q)return;let H=ln.current,se=H.style.height;H.style.height="auto";let re=H.getBoundingClientRect().height;H.style.height=se,vo(re),y(_t=>_t.find(Ot=>Ot.toastId===d.id)?_t.map(Ot=>Ot.toastId===d.id?{...Ot,height:re}:Ot):[{toastId:d.id,height:re,position:d.position},..._t])},[q,d.title,d.description,y,d.id]);let cn=M.useCallback(()=>{te(!0),yr(wr.current),y(H=>H.filter(se=>se.toastId!==d.id)),setTimeout(()=>{N(d)},Aw)},[d,N,y,wr]);M.useEffect(()=>{if(d.promise&&be==="loading"||d.duration===1/0||d.type==="loading")return;let H,se=Vu;return S||w||D&&Qu?(()=>{if(Hu.current<ka.current){let re=new Date().getTime()-ka.current;se=se-re}Hu.current=new Date().getTime()})():se!==1/0&&(ka.current=new Date().getTime(),H=setTimeout(()=>{var re;(re=d.onAutoClose)==null||re.call(d,d),cn()},se)),()=>clearTimeout(H)},[S,w,Q,d,Vu,cn,d.promise,be,D,Qu]),M.useEffect(()=>{let H=ln.current;if(H){let se=H.getBoundingClientRect().height;return vo(se),y(re=>[{toastId:d.id,height:se,position:d.position},...re]),()=>y(re=>re.filter(_t=>_t.toastId!==d.id))}},[y,d.id]),M.useEffect(()=>{d.delete&&cn()},[cn,d.delete]);function Iv(){return A!=null&&A.loading?M.createElement("div",{className:"sonner-loader","data-visible":be==="loading"},A.loading):B?M.createElement("div",{className:"sonner-loader","data-visible":be==="loading"},B):M.createElement(mw,{visible:be==="loading"})}return M.createElement("li",{"aria-live":d.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:ln,className:U($,_v,E==null?void 0:E.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,E==null?void 0:E.default,E==null?void 0:E[be],(n=d==null?void 0:d.classNames)==null?void 0:n[be]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:b,"data-styled":!(d.jsx||d.unstyled||x),"data-mounted":q,"data-promise":!!d.promise,"data-removed":Ze,"data-visible":ja,"data-y-position":Wu,"data-x-position":Mv,"data-index":h,"data-front":ba,"data-swiping":yt,"data-dismissible":xr,"data-type":be,"data-invert":Lv,"data-swipe-out":sn,"data-expanded":!!(S||Q&&q),style:{"--index":h,"--toasts-before":h,"--z-index":v.length-h,"--offset":`${Ze?bs:wr.current}px`,"--initial-height":Q?"auto":`${qn}px`,...j,...d.style},onPointerDown:H=>{Pa||!xr||(js.current=new Date,yr(wr.current),H.target.setPointerCapture(H.pointerId),H.target.tagName!=="BUTTON"&&(on(!0),Cr.current={x:H.clientX,y:H.clientY}))},onPointerUp:()=>{var H,se,re,_t;if(sn||!xr)return;Cr.current=null;let Ot=Number(((H=ln.current)==null?void 0:H.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Ps=new Date().getTime()-((se=js.current)==null?void 0:se.getTime()),Dv=Math.abs(Ot)/Ps;if(Math.abs(Ot)>=Ow||Dv>.11){yr(wr.current),(re=d.onDismiss)==null||re.call(d,d),cn(),an(!0);return}(_t=ln.current)==null||_t.style.setProperty("--swipe-amount","0px"),on(!1)},onPointerMove:H=>{var se;if(!Cr.current||!xr)return;let re=H.clientY-Cr.current.y,_t=H.clientX-Cr.current.x,Ot=(Wu==="top"?Math.min:Math.max)(0,re),Ps=H.pointerType==="touch"?10:2;Math.abs(Ot)>Ps?(se=ln.current)==null||se.style.setProperty("--swipe-amount",`${re}px`):Math.abs(_t)>Ps&&(Cr.current=null)}},Av&&!d.jsx?M.createElement("button",{"aria-label":z,"data-disabled":Pa,"data-close-button":!0,onClick:Pa||!xr?()=>{}:()=>{var H;cn(),(H=d.onDismiss)==null||H.call(d,d)},className:U(E==null?void 0:E.closeButton,(o=d==null?void 0:d.classNames)==null?void 0:o.closeButton)},M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},M.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),M.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,d.jsx||M.isValidElement(d.title)?d.jsx||d.title:M.createElement(M.Fragment,null,be||d.icon||d.promise?M.createElement("div",{"data-icon":"",className:U(E==null?void 0:E.icon,(s=d==null?void 0:d.classNames)==null?void 0:s.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||Iv():null,d.type!=="loading"?d.icon||(A==null?void 0:A[be])||pw(be):null):null,M.createElement("div",{"data-content":"",className:U(E==null?void 0:E.content,(i=d==null?void 0:d.classNames)==null?void 0:i.content)},M.createElement("div",{"data-title":"",className:U(E==null?void 0:E.title,(a=d==null?void 0:d.classNames)==null?void 0:a.title)},d.title),d.description?M.createElement("div",{"data-description":"",className:U(T,Ov,E==null?void 0:E.description,(c=d==null?void 0:d.classNames)==null?void 0:c.description)},d.description):null),M.isValidElement(d.cancel)?d.cancel:d.cancel&&qs(d.cancel)?M.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||R,onClick:H=>{var se,re;qs(d.cancel)&&xr&&((re=(se=d.cancel).onClick)==null||re.call(se,H),cn())},className:U(E==null?void 0:E.cancelButton,(u=d==null?void 0:d.classNames)==null?void 0:u.cancelButton)},d.cancel.label):null,M.isValidElement(d.action)?d.action:d.action&&qs(d.action)?M.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||O,onClick:H=>{var se,re;qs(d.action)&&(H.defaultPrevented||((re=(se=d.action).onClick)==null||re.call(se,H),cn()))},className:U(E==null?void 0:E.actionButton,(f=d==null?void 0:d.classNames)==null?void 0:f.actionButton)},d.action.label):null))};function yf(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var Iw=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:s,className:i,offset:a,theme:c="light",richColors:u,duration:f,style:p,visibleToasts:d=kw,toastOptions:x,dir:w=yf(),gap:y=_w,loadingIcon:C,icons:m,containerAriaLabel:h="Notifications",pauseWhenPageIsHidden:v,cn:S=Mw}=e,[N,b]=M.useState([]),k=M.useMemo(()=>Array.from(new Set([n].concat(N.filter(D=>D.position).map(D=>D.position)))),[N,n]),[j,R]=M.useState([]),[O,$]=M.useState(!1),[T,F]=M.useState(!1),[L,V]=M.useState(c!=="system"?c:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),B=M.useRef(null),Q=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=M.useRef(null),A=M.useRef(!1),z=M.useCallback(D=>{var U;(U=N.find(q=>q.id===D.id))!=null&&U.delete||et.dismiss(D.id),b(q=>q.filter(({id:de})=>de!==D.id))},[N]);return M.useEffect(()=>et.subscribe(D=>{if(D.dismiss){b(U=>U.map(q=>q.id===D.id?{...q,delete:!0}:q));return}setTimeout(()=>{pm.flushSync(()=>{b(U=>{let q=U.findIndex(de=>de.id===D.id);return q!==-1?[...U.slice(0,q),{...U[q],...D},...U.slice(q+1)]:[D,...U]})})})}),[]),M.useEffect(()=>{if(c!=="system"){V(c);return}c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?V("dark"):V("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:D})=>{V(D?"dark":"light")})},[c]),M.useEffect(()=>{N.length<=1&&$(!1)},[N]),M.useEffect(()=>{let D=U=>{var q,de;r.every(Ze=>U[Ze]||U.code===Ze)&&($(!0),(q=B.current)==null||q.focus()),U.code==="Escape"&&(document.activeElement===B.current||(de=B.current)!=null&&de.contains(document.activeElement))&&$(!1)};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[r]),M.useEffect(()=>{if(B.current)return()=>{E.current&&(E.current.focus({preventScroll:!0}),E.current=null,A.current=!1)}},[B.current]),N.length?M.createElement("section",{"aria-label":`${h} ${Q}`,tabIndex:-1},k.map((D,U)=>{var q;let[de,Ze]=D.split("-");return M.createElement("ol",{key:D,dir:w==="auto"?yf():w,tabIndex:-1,ref:B,className:i,"data-sonner-toaster":!0,"data-theme":L,"data-y-position":de,"data-x-position":Ze,style:{"--front-toast-height":`${((q=j[0])==null?void 0:q.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||Pw,"--width":`${Rw}px`,"--gap":`${y}px`,...p},onBlur:te=>{A.current&&!te.currentTarget.contains(te.relatedTarget)&&(A.current=!1,E.current&&(E.current.focus({preventScroll:!0}),E.current=null))},onFocus:te=>{te.target instanceof HTMLElement&&te.target.dataset.dismissible==="false"||A.current||(A.current=!0,E.current=te.relatedTarget)},onMouseEnter:()=>$(!0),onMouseMove:()=>$(!0),onMouseLeave:()=>{T||$(!1)},onPointerDown:te=>{te.target instanceof HTMLElement&&te.target.dataset.dismissible==="false"||F(!0)},onPointerUp:()=>F(!1)},N.filter(te=>!te.position&&U===0||te.position===D).map((te,yt)=>{var on,sn;return M.createElement(Lw,{key:te.id,icons:m,index:yt,toast:te,defaultRichColors:u,duration:(on=x==null?void 0:x.duration)!=null?on:f,className:x==null?void 0:x.className,descriptionClassName:x==null?void 0:x.descriptionClassName,invert:t,visibleToasts:d,closeButton:(sn=x==null?void 0:x.closeButton)!=null?sn:s,interacting:T,position:D,style:x==null?void 0:x.style,unstyled:x==null?void 0:x.unstyled,classNames:x==null?void 0:x.classNames,cancelButtonStyle:x==null?void 0:x.cancelButtonStyle,actionButtonStyle:x==null?void 0:x.actionButtonStyle,removeToast:z,toasts:N.filter(an=>an.position==te.position),heights:j.filter(an=>an.position==te.position),setHeights:R,expandByDefault:o,gap:y,loadingIcon:C,expanded:O,pauseWhenPageIsHidden:v,cn:S})}))})):null};const Dw=({...e})=>{const{theme:t="system"}=fw();return l.jsx(Iw,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var Fw=up.useId||(()=>{}),zw=0;function sg(e){const[t,n]=g.useState(Fw());return zn(()=>{e||n(r=>r??String(zw++))},[e]),e||(t?`radix-${t}`:"")}const $w=["top","right","bottom","left"],$n=Math.min,nt=Math.max,zi=Math.round,Ys=Math.floor,Bn=e=>({x:e,y:e}),Bw={left:"right",right:"left",bottom:"top",top:"bottom"},Uw={start:"end",end:"start"};function dc(e,t,n){return nt(e,$n(t,n))}function tn(e,t){return typeof e=="function"?e(t):e}function nn(e){return e.split("-")[0]}function po(e){return e.split("-")[1]}function Su(e){return e==="x"?"y":"x"}function Nu(e){return e==="y"?"height":"width"}function Un(e){return["top","bottom"].includes(nn(e))?"y":"x"}function Eu(e){return Su(Un(e))}function Vw(e,t,n){n===void 0&&(n=!1);const r=po(e),o=Eu(e),s=Nu(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=$i(i)),[i,$i(i)]}function Hw(e){const t=$i(e);return[fc(e),t,fc(t)]}function fc(e){return e.replace(/start|end/g,t=>Uw[t])}function Ww(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function Gw(e,t,n,r){const o=po(e);let s=Ww(nn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(fc)))),s}function $i(e){return e.replace(/left|right|bottom|top/g,t=>Bw[t])}function Qw(e){return{top:0,right:0,bottom:0,left:0,...e}}function ig(e){return typeof e!="number"?Qw(e):{top:e,right:e,bottom:e,left:e}}function Bi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function xf(e,t,n){let{reference:r,floating:o}=e;const s=Un(t),i=Eu(t),a=Nu(i),c=nn(t),u=s==="y",f=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,d=r[a]/2-o[a]/2;let x;switch(c){case"top":x={x:f,y:r.y-o.height};break;case"bottom":x={x:f,y:r.y+r.height};break;case"right":x={x:r.x+r.width,y:p};break;case"left":x={x:r.x-o.width,y:p};break;default:x={x:r.x,y:r.y}}switch(po(t)){case"start":x[i]-=d*(n&&u?-1:1);break;case"end":x[i]+=d*(n&&u?-1:1);break}return x}const Kw=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),c=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:p}=xf(u,r,c),d=r,x={},w=0;for(let y=0;y<a.length;y++){const{name:C,fn:m}=a[y],{x:h,y:v,data:S,reset:N}=await m({x:f,y:p,initialPlacement:r,placement:d,strategy:o,middlewareData:x,rects:u,platform:i,elements:{reference:e,floating:t}});f=h??f,p=v??p,x={...x,[C]:{...x[C],...S}},N&&w<=50&&(w++,typeof N=="object"&&(N.placement&&(d=N.placement),N.rects&&(u=N.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):N.rects),{x:f,y:p}=xf(u,d,c)),y=-1)}return{x:f,y:p,placement:d,strategy:o,middlewareData:x}};async function ls(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:d=!1,padding:x=0}=tn(t,e),w=ig(x),C=a[d?p==="floating"?"reference":"floating":p],m=Bi(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(C)))==null||n?C:C.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:c})),h=p==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,v=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),S=await(s.isElement==null?void 0:s.isElement(v))?await(s.getScale==null?void 0:s.getScale(v))||{x:1,y:1}:{x:1,y:1},N=Bi(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:h,offsetParent:v,strategy:c}):h);return{top:(m.top-N.top+w.top)/S.y,bottom:(N.bottom-m.bottom+w.bottom)/S.y,left:(m.left-N.left+w.left)/S.x,right:(N.right-m.right+w.right)/S.x}}const qw=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:c}=t,{element:u,padding:f=0}=tn(e,t)||{};if(u==null)return{};const p=ig(f),d={x:n,y:r},x=Eu(o),w=Nu(x),y=await i.getDimensions(u),C=x==="y",m=C?"top":"left",h=C?"bottom":"right",v=C?"clientHeight":"clientWidth",S=s.reference[w]+s.reference[x]-d[x]-s.floating[w],N=d[x]-s.reference[x],b=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let k=b?b[v]:0;(!k||!await(i.isElement==null?void 0:i.isElement(b)))&&(k=a.floating[v]||s.floating[w]);const j=S/2-N/2,R=k/2-y[w]/2-1,O=$n(p[m],R),$=$n(p[h],R),T=O,F=k-y[w]-$,L=k/2-y[w]/2+j,V=dc(T,L,F),B=!c.arrow&&po(o)!=null&&L!==V&&s.reference[w]/2-(L<T?O:$)-y[w]/2<0,Q=B?L<T?L-T:L-F:0;return{[x]:d[x]+Q,data:{[x]:V,centerOffset:L-V-Q,...B&&{alignmentOffset:Q}},reset:B}}}),Yw=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:c,elements:u}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:d,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:y=!0,...C}=tn(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const m=nn(o),h=Un(a),v=nn(a)===a,S=await(c.isRTL==null?void 0:c.isRTL(u.floating)),N=d||(v||!y?[$i(a)]:Hw(a)),b=w!=="none";!d&&b&&N.push(...Gw(a,y,w,S));const k=[a,...N],j=await ls(t,C),R=[];let O=((r=s.flip)==null?void 0:r.overflows)||[];if(f&&R.push(j[m]),p){const L=Vw(o,i,S);R.push(j[L[0]],j[L[1]])}if(O=[...O,{placement:o,overflows:R}],!R.every(L=>L<=0)){var $,T;const L=((($=s.flip)==null?void 0:$.index)||0)+1,V=k[L];if(V)return{data:{index:L,overflows:O},reset:{placement:V}};let B=(T=O.filter(Q=>Q.overflows[0]<=0).sort((Q,E)=>Q.overflows[1]-E.overflows[1])[0])==null?void 0:T.placement;if(!B)switch(x){case"bestFit":{var F;const Q=(F=O.filter(E=>{if(b){const A=Un(E.placement);return A===h||A==="y"}return!0}).map(E=>[E.placement,E.overflows.filter(A=>A>0).reduce((A,z)=>A+z,0)]).sort((E,A)=>E[1]-A[1])[0])==null?void 0:F[0];Q&&(B=Q);break}case"initialPlacement":B=a;break}if(o!==B)return{reset:{placement:B}}}return{}}}};function wf(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Cf(e){return $w.some(t=>e[t]>=0)}const Xw=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=tn(e,t);switch(r){case"referenceHidden":{const s=await ls(t,{...o,elementContext:"reference"}),i=wf(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Cf(i)}}}case"escaped":{const s=await ls(t,{...o,altBoundary:!0}),i=wf(s,n.floating);return{data:{escapedOffsets:i,escaped:Cf(i)}}}default:return{}}}}};async function Zw(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=nn(n),a=po(n),c=Un(n)==="y",u=["left","top"].includes(i)?-1:1,f=s&&c?-1:1,p=tn(t,e);let{mainAxis:d,crossAxis:x,alignmentAxis:w}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&typeof w=="number"&&(x=a==="end"?w*-1:w),c?{x:x*f,y:d*u}:{x:d*u,y:x*f}}const Jw=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,c=await Zw(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+c.x,y:s+c.y,data:{...c,placement:i}}}}},e2=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:C=>{let{x:m,y:h}=C;return{x:m,y:h}}},...c}=tn(e,t),u={x:n,y:r},f=await ls(t,c),p=Un(nn(o)),d=Su(p);let x=u[d],w=u[p];if(s){const C=d==="y"?"top":"left",m=d==="y"?"bottom":"right",h=x+f[C],v=x-f[m];x=dc(h,x,v)}if(i){const C=p==="y"?"top":"left",m=p==="y"?"bottom":"right",h=w+f[C],v=w-f[m];w=dc(h,w,v)}const y=a.fn({...t,[d]:x,[p]:w});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[d]:s,[p]:i}}}}}},t2=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=tn(e,t),f={x:n,y:r},p=Un(o),d=Su(p);let x=f[d],w=f[p];const y=tn(a,t),C=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(c){const v=d==="y"?"height":"width",S=s.reference[d]-s.floating[v]+C.mainAxis,N=s.reference[d]+s.reference[v]-C.mainAxis;x<S?x=S:x>N&&(x=N)}if(u){var m,h;const v=d==="y"?"width":"height",S=["top","left"].includes(nn(o)),N=s.reference[p]-s.floating[v]+(S&&((m=i.offset)==null?void 0:m[p])||0)+(S?0:C.crossAxis),b=s.reference[p]+s.reference[v]+(S?0:((h=i.offset)==null?void 0:h[p])||0)-(S?C.crossAxis:0);w<N?w=N:w>b&&(w=b)}return{[d]:x,[p]:w}}}},n2=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:c=()=>{},...u}=tn(e,t),f=await ls(t,u),p=nn(o),d=po(o),x=Un(o)==="y",{width:w,height:y}=s.floating;let C,m;p==="top"||p==="bottom"?(C=p,m=d===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(m=p,C=d==="end"?"top":"bottom");const h=y-f.top-f.bottom,v=w-f.left-f.right,S=$n(y-f[C],h),N=$n(w-f[m],v),b=!t.middlewareData.shift;let k=S,j=N;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(j=v),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(k=h),b&&!d){const O=nt(f.left,0),$=nt(f.right,0),T=nt(f.top,0),F=nt(f.bottom,0);x?j=w-2*(O!==0||$!==0?O+$:nt(f.left,f.right)):k=y-2*(T!==0||F!==0?T+F:nt(f.top,f.bottom))}await c({...t,availableWidth:j,availableHeight:k});const R=await i.getDimensions(a.floating);return w!==R.width||y!==R.height?{reset:{rects:!0}}:{}}}};function ga(){return typeof window<"u"}function ho(e){return ag(e)?(e.nodeName||"").toLowerCase():"#document"}function st(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Vt(e){var t;return(t=(ag(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function ag(e){return ga()?e instanceof Node||e instanceof st(e).Node:!1}function Tt(e){return ga()?e instanceof Element||e instanceof st(e).Element:!1}function Bt(e){return ga()?e instanceof HTMLElement||e instanceof st(e).HTMLElement:!1}function Sf(e){return!ga()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof st(e).ShadowRoot}function Ns(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Rt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function r2(e){return["table","td","th"].includes(ho(e))}function va(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function bu(e){const t=ju(),n=Tt(e)?Rt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function o2(e){let t=Vn(e);for(;Bt(t)&&!io(t);){if(bu(t))return t;if(va(t))return null;t=Vn(t)}return null}function ju(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function io(e){return["html","body","#document"].includes(ho(e))}function Rt(e){return st(e).getComputedStyle(e)}function ya(e){return Tt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Vn(e){if(ho(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Sf(e)&&e.host||Vt(e);return Sf(t)?t.host:t}function lg(e){const t=Vn(e);return io(t)?e.ownerDocument?e.ownerDocument.body:e.body:Bt(t)&&Ns(t)?t:lg(t)}function cs(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=lg(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=st(o);if(s){const a=pc(i);return t.concat(i,i.visualViewport||[],Ns(o)?o:[],a&&n?cs(a):[])}return t.concat(o,cs(o,[],n))}function pc(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function cg(e){const t=Rt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Bt(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=zi(n)!==s||zi(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function ku(e){return Tt(e)?e:e.contextElement}function Vr(e){const t=ku(e);if(!Bt(t))return Bn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=cg(t);let i=(s?zi(n.width):n.width)/r,a=(s?zi(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const s2=Bn(0);function ug(e){const t=st(e);return!ju()||!t.visualViewport?s2:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function i2(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==st(e)?!1:t}function pr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=ku(e);let i=Bn(1);t&&(r?Tt(r)&&(i=Vr(r)):i=Vr(e));const a=i2(s,n,r)?ug(s):Bn(0);let c=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,f=o.width/i.x,p=o.height/i.y;if(s){const d=st(s),x=r&&Tt(r)?st(r):r;let w=d,y=pc(w);for(;y&&r&&x!==w;){const C=Vr(y),m=y.getBoundingClientRect(),h=Rt(y),v=m.left+(y.clientLeft+parseFloat(h.paddingLeft))*C.x,S=m.top+(y.clientTop+parseFloat(h.paddingTop))*C.y;c*=C.x,u*=C.y,f*=C.x,p*=C.y,c+=v,u+=S,w=st(y),y=pc(w)}}return Bi({width:f,height:p,x:c,y:u})}function a2(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Vt(r),a=t?va(t.floating):!1;if(r===i||a&&s)return n;let c={scrollLeft:0,scrollTop:0},u=Bn(1);const f=Bn(0),p=Bt(r);if((p||!p&&!s)&&((ho(r)!=="body"||Ns(i))&&(c=ya(r)),Bt(r))){const d=pr(r);u=Vr(r),f.x=d.x+r.clientLeft,f.y=d.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+f.x,y:n.y*u.y-c.scrollTop*u.y+f.y}}function l2(e){return Array.from(e.getClientRects())}function hc(e,t){const n=ya(e).scrollLeft;return t?t.left+n:pr(Vt(e)).left+n}function c2(e){const t=Vt(e),n=ya(e),r=e.ownerDocument.body,o=nt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=nt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+hc(e);const a=-n.scrollTop;return Rt(r).direction==="rtl"&&(i+=nt(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function u2(e,t){const n=st(e),r=Vt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,c=0;if(o){s=o.width,i=o.height;const u=ju();(!u||u&&t==="fixed")&&(a=o.offsetLeft,c=o.offsetTop)}return{width:s,height:i,x:a,y:c}}function d2(e,t){const n=pr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Bt(e)?Vr(e):Bn(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,c=o*s.x,u=r*s.y;return{width:i,height:a,x:c,y:u}}function Nf(e,t,n){let r;if(t==="viewport")r=u2(e,n);else if(t==="document")r=c2(Vt(e));else if(Tt(t))r=d2(t,n);else{const o=ug(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Bi(r)}function dg(e,t){const n=Vn(e);return n===t||!Tt(n)||io(n)?!1:Rt(n).position==="fixed"||dg(n,t)}function f2(e,t){const n=t.get(e);if(n)return n;let r=cs(e,[],!1).filter(a=>Tt(a)&&ho(a)!=="body"),o=null;const s=Rt(e).position==="fixed";let i=s?Vn(e):e;for(;Tt(i)&&!io(i);){const a=Rt(i),c=bu(i);!c&&a.position==="fixed"&&(o=null),(s?!c&&!o:!c&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ns(i)&&!c&&dg(e,i))?r=r.filter(f=>f!==i):o=a,i=Vn(i)}return t.set(e,r),r}function p2(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?va(t)?[]:f2(t,this._c):[].concat(n),r],a=i[0],c=i.reduce((u,f)=>{const p=Nf(t,f,o);return u.top=nt(p.top,u.top),u.right=$n(p.right,u.right),u.bottom=$n(p.bottom,u.bottom),u.left=nt(p.left,u.left),u},Nf(t,a,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function h2(e){const{width:t,height:n}=cg(e);return{width:t,height:n}}function m2(e,t,n){const r=Bt(t),o=Vt(t),s=n==="fixed",i=pr(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const c=Bn(0);if(r||!r&&!s)if((ho(t)!=="body"||Ns(o))&&(a=ya(t)),r){const x=pr(t,!0,s,t);c.x=x.x+t.clientLeft,c.y=x.y+t.clientTop}else o&&(c.x=hc(o));let u=0,f=0;if(o&&!r&&!s){const x=o.getBoundingClientRect();f=x.top+a.scrollTop,u=x.left+a.scrollLeft-hc(o,x)}const p=i.left+a.scrollLeft-c.x-u,d=i.top+a.scrollTop-c.y-f;return{x:p,y:d,width:i.width,height:i.height}}function al(e){return Rt(e).position==="static"}function Ef(e,t){if(!Bt(e)||Rt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Vt(e)===n&&(n=n.ownerDocument.body),n}function fg(e,t){const n=st(e);if(va(e))return n;if(!Bt(e)){let o=Vn(e);for(;o&&!io(o);){if(Tt(o)&&!al(o))return o;o=Vn(o)}return n}let r=Ef(e,t);for(;r&&r2(r)&&al(r);)r=Ef(r,t);return r&&io(r)&&al(r)&&!bu(r)?n:r||o2(e)||n}const g2=async function(e){const t=this.getOffsetParent||fg,n=this.getDimensions,r=await n(e.floating);return{reference:m2(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function v2(e){return Rt(e).direction==="rtl"}const y2={convertOffsetParentRelativeRectToViewportRelativeRect:a2,getDocumentElement:Vt,getClippingRect:p2,getOffsetParent:fg,getElementRects:g2,getClientRects:l2,getDimensions:h2,getScale:Vr,isElement:Tt,isRTL:v2};function x2(e,t){let n=null,r;const o=Vt(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,c){a===void 0&&(a=!1),c===void 0&&(c=1),s();const{left:u,top:f,width:p,height:d}=e.getBoundingClientRect();if(a||t(),!p||!d)return;const x=Ys(f),w=Ys(o.clientWidth-(u+p)),y=Ys(o.clientHeight-(f+d)),C=Ys(u),h={rootMargin:-x+"px "+-w+"px "+-y+"px "+-C+"px",threshold:nt(0,$n(1,c))||1};let v=!0;function S(N){const b=N[0].intersectionRatio;if(b!==c){if(!v)return i();b?i(!1,b):r=setTimeout(()=>{i(!1,1e-7)},1e3)}v=!1}try{n=new IntersectionObserver(S,{...h,root:o.ownerDocument})}catch{n=new IntersectionObserver(S,h)}n.observe(e)}return i(!0),s}function w2(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,u=ku(e),f=o||s?[...u?cs(u):[],...cs(t)]:[];f.forEach(m=>{o&&m.addEventListener("scroll",n,{passive:!0}),s&&m.addEventListener("resize",n)});const p=u&&a?x2(u,n):null;let d=-1,x=null;i&&(x=new ResizeObserver(m=>{let[h]=m;h&&h.target===u&&x&&(x.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var v;(v=x)==null||v.observe(t)})),n()}),u&&!c&&x.observe(u),x.observe(t));let w,y=c?pr(e):null;c&&C();function C(){const m=pr(e);y&&(m.x!==y.x||m.y!==y.y||m.width!==y.width||m.height!==y.height)&&n(),y=m,w=requestAnimationFrame(C)}return n(),()=>{var m;f.forEach(h=>{o&&h.removeEventListener("scroll",n),s&&h.removeEventListener("resize",n)}),p==null||p(),(m=x)==null||m.disconnect(),x=null,c&&cancelAnimationFrame(w)}}const C2=Jw,S2=e2,N2=Yw,E2=n2,b2=Xw,bf=qw,j2=t2,k2=(e,t,n)=>{const r=new Map,o={platform:y2,...n},s={...o.platform,_c:r};return Kw(e,t,{...o,platform:s})};var fi=typeof document<"u"?g.useLayoutEffect:g.useEffect;function Ui(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ui(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Ui(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function pg(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function jf(e,t){const n=pg(e);return Math.round(t*n)/n}function ll(e){const t=g.useRef(e);return fi(()=>{t.current=e}),t}function P2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:c,open:u}=e,[f,p]=g.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,x]=g.useState(r);Ui(d,r)||x(r);const[w,y]=g.useState(null),[C,m]=g.useState(null),h=g.useCallback(E=>{E!==b.current&&(b.current=E,y(E))},[]),v=g.useCallback(E=>{E!==k.current&&(k.current=E,m(E))},[]),S=s||w,N=i||C,b=g.useRef(null),k=g.useRef(null),j=g.useRef(f),R=c!=null,O=ll(c),$=ll(o),T=ll(u),F=g.useCallback(()=>{if(!b.current||!k.current)return;const E={placement:t,strategy:n,middleware:d};$.current&&(E.platform=$.current),k2(b.current,k.current,E).then(A=>{const z={...A,isPositioned:T.current!==!1};L.current&&!Ui(j.current,z)&&(j.current=z,Cs.flushSync(()=>{p(z)}))})},[d,t,n,$,T]);fi(()=>{u===!1&&j.current.isPositioned&&(j.current.isPositioned=!1,p(E=>({...E,isPositioned:!1})))},[u]);const L=g.useRef(!1);fi(()=>(L.current=!0,()=>{L.current=!1}),[]),fi(()=>{if(S&&(b.current=S),N&&(k.current=N),S&&N){if(O.current)return O.current(S,N,F);F()}},[S,N,F,O,R]);const V=g.useMemo(()=>({reference:b,floating:k,setReference:h,setFloating:v}),[h,v]),B=g.useMemo(()=>({reference:S,floating:N}),[S,N]),Q=g.useMemo(()=>{const E={position:n,left:0,top:0};if(!B.floating)return E;const A=jf(B.floating,f.x),z=jf(B.floating,f.y);return a?{...E,transform:"translate("+A+"px, "+z+"px)",...pg(B.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:A,top:z}},[n,a,B.floating,f.x,f.y]);return g.useMemo(()=>({...f,update:F,refs:V,elements:B,floatingStyles:Q}),[f,F,V,B,Q])}const T2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?bf({element:r.current,padding:o}).fn(n):{}:r?bf({element:r,padding:o}).fn(n):{}}}},R2=(e,t)=>({...C2(e),options:[e,t]}),_2=(e,t)=>({...S2(e),options:[e,t]}),O2=(e,t)=>({...j2(e),options:[e,t]}),A2=(e,t)=>({...N2(e),options:[e,t]}),M2=(e,t)=>({...E2(e),options:[e,t]}),L2=(e,t)=>({...b2(e),options:[e,t]}),I2=(e,t)=>({...T2(e),options:[e,t]});var D2="Arrow",hg=g.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return l.jsx(ce.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});hg.displayName=D2;var F2=hg;function z2(e,t=[]){let n=[];function r(s,i){const a=g.createContext(i),c=n.length;n=[...n,i];function u(p){const{scope:d,children:x,...w}=p,y=(d==null?void 0:d[e][c])||a,C=g.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:C,children:x})}function f(p,d){const x=(d==null?void 0:d[e][c])||a,w=g.useContext(x);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,f]}const o=()=>{const s=n.map(i=>g.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,$2(o,...t)]}function $2(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function mg(e){const[t,n]=g.useState(void 0);return zn(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const c=s.borderBoxSize,u=Array.isArray(c)?c[0]:c;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var gg="Popper",[vg,yg]=z2(gg),[IN,xg]=vg(gg),wg="PopperAnchor",Cg=g.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=xg(wg,n),i=g.useRef(null),a=at(t,i);return g.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:l.jsx(ce.div,{...o,ref:a})});Cg.displayName=wg;var Pu="PopperContent",[B2,U2]=vg(Pu),Sg=g.forwardRef((e,t)=>{var yt,on,sn,an,bs,yr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:c=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:d=!1,updatePositionStrategy:x="optimized",onPlaced:w,...y}=e,C=xg(Pu,n),[m,h]=g.useState(null),v=at(t,qn=>h(qn)),[S,N]=g.useState(null),b=mg(S),k=(b==null?void 0:b.width)??0,j=(b==null?void 0:b.height)??0,R=r+(s!=="center"?"-"+s:""),O=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},$=Array.isArray(u)?u:[u],T=$.length>0,F={padding:O,boundary:$.filter(H2),altBoundary:T},{refs:L,floatingStyles:V,placement:B,isPositioned:Q,middlewareData:E}=P2({strategy:"fixed",placement:R,whileElementsMounted:(...qn)=>w2(...qn,{animationFrame:x==="always"}),elements:{reference:C.anchor},middleware:[R2({mainAxis:o+j,alignmentAxis:i}),c&&_2({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?O2():void 0,...F}),c&&A2({...F}),M2({...F,apply:({elements:qn,rects:vo,availableWidth:js,availableHeight:ln})=>{const{width:ba,height:ja}=vo.reference,be=qn.floating.style;be.setProperty("--radix-popper-available-width",`${js}px`),be.setProperty("--radix-popper-available-height",`${ln}px`),be.setProperty("--radix-popper-anchor-width",`${ba}px`),be.setProperty("--radix-popper-anchor-height",`${ja}px`)}}),S&&I2({element:S,padding:a}),W2({arrowWidth:k,arrowHeight:j}),d&&L2({strategy:"referenceHidden",...F})]}),[A,z]=bg(B),D=Pt(w);zn(()=>{Q&&(D==null||D())},[Q,D]);const U=(yt=E.arrow)==null?void 0:yt.x,q=(on=E.arrow)==null?void 0:on.y,de=((sn=E.arrow)==null?void 0:sn.centerOffset)!==0,[Ze,te]=g.useState();return zn(()=>{m&&te(window.getComputedStyle(m).zIndex)},[m]),l.jsx("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:Q?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ze,"--radix-popper-transform-origin":[(an=E.transformOrigin)==null?void 0:an.x,(bs=E.transformOrigin)==null?void 0:bs.y].join(" "),...((yr=E.hide)==null?void 0:yr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:l.jsx(B2,{scope:n,placedSide:A,onArrowChange:N,arrowX:U,arrowY:q,shouldHideArrow:de,children:l.jsx(ce.div,{"data-side":A,"data-align":z,...y,ref:v,style:{...y.style,animation:Q?void 0:"none"}})})})});Sg.displayName=Pu;var Ng="PopperArrow",V2={top:"bottom",right:"left",bottom:"top",left:"right"},Eg=g.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=U2(Ng,r),i=V2[s.placedSide];return l.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:l.jsx(F2,{...o,ref:n,style:{...o.style,display:"block"}})})});Eg.displayName=Ng;function H2(e){return e!==null}var W2=e=>({name:"transformOrigin",options:e,fn(t){var C,m,h;const{placement:n,rects:r,middlewareData:o}=t,i=((C=o.arrow)==null?void 0:C.centerOffset)!==0,a=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[u,f]=bg(n),p={start:"0%",center:"50%",end:"100%"}[f],d=(((m=o.arrow)==null?void 0:m.x)??0)+a/2,x=(((h=o.arrow)==null?void 0:h.y)??0)+c/2;let w="",y="";return u==="bottom"?(w=i?p:`${d}px`,y=`${-c}px`):u==="top"?(w=i?p:`${d}px`,y=`${r.floating.height+c}px`):u==="right"?(w=`${-c}px`,y=i?p:`${x}px`):u==="left"&&(w=`${r.floating.width+c}px`,y=i?p:`${x}px`),{data:{x:w,y}}}});function bg(e){const[t,n="center"]=e.split("-");return[t,n]}var G2=Cg,Q2=Sg,K2=Eg,[xa,DN]=ca("Tooltip",[yg]),Tu=yg(),jg="TooltipProvider",q2=700,kf="tooltip.open",[Y2,kg]=xa(jg),Pg=e=>{const{__scopeTooltip:t,delayDuration:n=q2,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,a]=g.useState(!0),c=g.useRef(!1),u=g.useRef(0);return g.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),l.jsx(Y2,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:g.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:g.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:g.useCallback(f=>{c.current=f},[]),disableHoverableContent:o,children:s})};Pg.displayName=jg;var Tg="Tooltip",[FN,wa]=xa(Tg),mc="TooltipTrigger",X2=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=wa(mc,n),s=kg(mc,n),i=Tu(n),a=g.useRef(null),c=at(t,a,o.onTriggerChange),u=g.useRef(!1),f=g.useRef(!1),p=g.useCallback(()=>u.current=!1,[]);return g.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),l.jsx(G2,{asChild:!0,...i,children:l.jsx(ce.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:c,onPointerMove:ee(e.onPointerMove,d=>{d.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:ee(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:ee(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:ee(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ee(e.onBlur,o.onClose),onClick:ee(e.onClick,o.onClose)})})});X2.displayName=mc;var Z2="TooltipPortal",[zN,J2]=xa(Z2,{forceMount:void 0}),ao="TooltipContent",Rg=g.forwardRef((e,t)=>{const n=J2(ao,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=wa(ao,e.__scopeTooltip);return l.jsx(Ss,{present:r||i.open,children:i.disableHoverableContent?l.jsx(_g,{side:o,...s,ref:t}):l.jsx(eC,{side:o,...s,ref:t})})}),eC=g.forwardRef((e,t)=>{const n=wa(ao,e.__scopeTooltip),r=kg(ao,e.__scopeTooltip),o=g.useRef(null),s=at(t,o),[i,a]=g.useState(null),{trigger:c,onClose:u}=n,f=o.current,{onPointerInTransitChange:p}=r,d=g.useCallback(()=>{a(null),p(!1)},[p]),x=g.useCallback((w,y)=>{const C=w.currentTarget,m={x:w.clientX,y:w.clientY},h=oC(m,C.getBoundingClientRect()),v=sC(m,h),S=iC(y.getBoundingClientRect()),N=lC([...v,...S]);a(N),p(!0)},[p]);return g.useEffect(()=>()=>d(),[d]),g.useEffect(()=>{if(c&&f){const w=C=>x(C,f),y=C=>x(C,c);return c.addEventListener("pointerleave",w),f.addEventListener("pointerleave",y),()=>{c.removeEventListener("pointerleave",w),f.removeEventListener("pointerleave",y)}}},[c,f,x,d]),g.useEffect(()=>{if(i){const w=y=>{const C=y.target,m={x:y.clientX,y:y.clientY},h=(c==null?void 0:c.contains(C))||(f==null?void 0:f.contains(C)),v=!aC(m,i);h?d():v&&(d(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[c,f,i,u,d]),l.jsx(_g,{...e,ref:s})}),[tC,nC]=xa(Tg,{isInside:!1}),_g=g.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,c=wa(ao,n),u=Tu(n),{onClose:f}=c;return g.useEffect(()=>(document.addEventListener(kf,f),()=>document.removeEventListener(kf,f)),[f]),g.useEffect(()=>{if(c.trigger){const p=d=>{const x=d.target;x!=null&&x.contains(c.trigger)&&f()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[c.trigger,f]),l.jsx(vu,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:p=>p.preventDefault(),onDismiss:f,children:l.jsxs(Q2,{"data-state":c.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(gm,{children:r}),l.jsx(tC,{scope:n,isInside:!0,children:l.jsx(Bx,{id:c.contentId,role:"tooltip",children:o||r})})]})})});Rg.displayName=ao;var Og="TooltipArrow",rC=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Tu(n);return nC(Og,n).isInside?null:l.jsx(K2,{...o,...r,ref:t})});rC.displayName=Og;function oC(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function sC(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function iC(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function aC(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,c=t[s].y,u=t[i].x,f=t[i].y;c>r!=f>r&&n<(u-a)*(r-c)/(f-c)+a&&(o=!o)}return o}function lC(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),cC(t)}function cC(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var uC=Pg,Ag=Rg;const dC=uC,fC=g.forwardRef(({className:e,sideOffset:t=4,...n},r)=>l.jsx(Ag,{ref:r,sideOffset:t,className:me("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));fC.displayName=Ag.displayName;var Ca=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Sa=typeof window>"u"||"Deno"in globalThis;function Ct(){}function pC(e,t){return typeof e=="function"?e(t):e}function hC(e){return typeof e=="number"&&e>=0&&e!==1/0}function mC(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Pf(e,t){return typeof e=="function"?e(t):e}function gC(e,t){return typeof e=="function"?e(t):e}function Tf(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:a}=e;if(i){if(r){if(t.queryHash!==Ru(i,t.options))return!1}else if(!ds(t.queryKey,i))return!1}if(n!=="all"){const c=t.isActive();if(n==="active"&&!c||n==="inactive"&&c)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||s&&!s(t))}function Rf(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(us(t.options.mutationKey)!==us(s))return!1}else if(!ds(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function Ru(e,t){return((t==null?void 0:t.queryKeyHashFn)||us)(e)}function us(e){return JSON.stringify(e,(t,n)=>gc(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function ds(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!ds(e[n],t[n])):!1}function Mg(e,t){if(e===t)return e;const n=_f(e)&&_f(t);if(n||gc(e)&&gc(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,a=n?[]:{};let c=0;for(let u=0;u<i;u++){const f=n?u:s[u];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(a[f]=void 0,c++):(a[f]=Mg(e[f],t[f]),a[f]===e[f]&&e[f]!==void 0&&c++)}return o===i&&c===o?e:a}return t}function _f(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function gc(e){if(!Of(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Of(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Of(e){return Object.prototype.toString.call(e)==="[object Object]"}function vC(e){return new Promise(t=>{setTimeout(t,e)})}function yC(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Mg(e,t):t}function xC(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function wC(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var _u=Symbol();function Lg(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===_u?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var tr,xn,Hr,Wf,CC=(Wf=class extends Ca{constructor(){super();J(this,tr);J(this,xn);J(this,Hr);G(this,Hr,t=>{if(!Sa&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){P(this,xn)||this.setEventListener(P(this,Hr))}onUnsubscribe(){var t;this.hasListeners()||((t=P(this,xn))==null||t.call(this),G(this,xn,void 0))}setEventListener(t){var n;G(this,Hr,t),(n=P(this,xn))==null||n.call(this),G(this,xn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){P(this,tr)!==t&&(G(this,tr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof P(this,tr)=="boolean"?P(this,tr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},tr=new WeakMap,xn=new WeakMap,Hr=new WeakMap,Wf),Ig=new CC,Wr,wn,Gr,Gf,SC=(Gf=class extends Ca{constructor(){super();J(this,Wr,!0);J(this,wn);J(this,Gr);G(this,Gr,t=>{if(!Sa&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){P(this,wn)||this.setEventListener(P(this,Gr))}onUnsubscribe(){var t;this.hasListeners()||((t=P(this,wn))==null||t.call(this),G(this,wn,void 0))}setEventListener(t){var n;G(this,Gr,t),(n=P(this,wn))==null||n.call(this),G(this,wn,t(this.setOnline.bind(this)))}setOnline(t){P(this,Wr)!==t&&(G(this,Wr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return P(this,Wr)}},Wr=new WeakMap,wn=new WeakMap,Gr=new WeakMap,Gf),Vi=new SC;function NC(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function EC(e){return Math.min(1e3*2**e,3e4)}function Dg(e){return(e??"online")==="online"?Vi.isOnline():!0}var Fg=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function cl(e){return e instanceof Fg}function zg(e){let t=!1,n=0,r=!1,o;const s=NC(),i=y=>{var C;r||(d(new Fg(y)),(C=e.abort)==null||C.call(e))},a=()=>{t=!0},c=()=>{t=!1},u=()=>Ig.isFocused()&&(e.networkMode==="always"||Vi.isOnline())&&e.canRun(),f=()=>Dg(e.networkMode)&&e.canRun(),p=y=>{var C;r||(r=!0,(C=e.onSuccess)==null||C.call(e,y),o==null||o(),s.resolve(y))},d=y=>{var C;r||(r=!0,(C=e.onError)==null||C.call(e,y),o==null||o(),s.reject(y))},x=()=>new Promise(y=>{var C;o=m=>{(r||u())&&y(m)},(C=e.onPause)==null||C.call(e)}).then(()=>{var y;o=void 0,r||(y=e.onContinue)==null||y.call(e)}),w=()=>{if(r)return;let y;const C=n===0?e.initialPromise:void 0;try{y=C??e.fn()}catch(m){y=Promise.reject(m)}Promise.resolve(y).then(p).catch(m=>{var b;if(r)return;const h=e.retry??(Sa?0:3),v=e.retryDelay??EC,S=typeof v=="function"?v(n,m):v,N=h===!0||typeof h=="number"&&n<h||typeof h=="function"&&h(n,m);if(t||!N){d(m);return}n++,(b=e.onFail)==null||b.call(e,n,m),vC(S).then(()=>u()?void 0:x()).then(()=>{t?d(m):w()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:a,continueRetry:c,canStart:f,start:()=>(f()?w():x().then(w),s)}}function bC(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const s=a=>{t?e.push(a):o(()=>{n(a)})},i=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(c=>{n(c)})})})};return{batch:a=>{let c;t++;try{c=a()}finally{t--,t||i()}return c},batchCalls:a=>(...c)=>{s(()=>{a(...c)})},schedule:s,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var Be=bC(),nr,Qf,$g=(Qf=class{constructor(){J(this,nr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),hC(this.gcTime)&&G(this,nr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Sa?1/0:5*60*1e3))}clearGcTimeout(){P(this,nr)&&(clearTimeout(P(this,nr)),G(this,nr,void 0))}},nr=new WeakMap,Qf),Qr,Kr,ut,Ie,hs,rr,St,Wt,Kf,jC=(Kf=class extends $g{constructor(t){super();J(this,St);J(this,Qr);J(this,Kr);J(this,ut);J(this,Ie);J(this,hs);J(this,rr);G(this,rr,!1),G(this,hs,t.defaultOptions),this.setOptions(t.options),this.observers=[],G(this,ut,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,G(this,Qr,PC(this.options)),this.state=t.state??P(this,Qr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=P(this,Ie))==null?void 0:t.promise}setOptions(t){this.options={...P(this,hs),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&P(this,ut).remove(this)}setData(t,n){const r=yC(this.state.data,t,this.options);return Ae(this,St,Wt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Ae(this,St,Wt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=P(this,Ie))==null?void 0:r.promise;return(o=P(this,Ie))==null||o.cancel(t),n?n.then(Ct).catch(Ct):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(P(this,Qr))}isActive(){return this.observers.some(t=>gC(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===_u||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!mC(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=P(this,Ie))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=P(this,Ie))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),P(this,ut).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(P(this,Ie)&&(P(this,rr)?P(this,Ie).cancel({revert:!0}):P(this,Ie).cancelRetry()),this.scheduleGc()),P(this,ut).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Ae(this,St,Wt).call(this,{type:"invalidate"})}fetch(t,n){var c,u,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(P(this,Ie))return P(this,Ie).continueRetry(),P(this,Ie).promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(d=>d.options.queryFn);p&&this.setOptions(p.options)}const r=new AbortController,o=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(G(this,rr,!0),r.signal)})},s=()=>{const p=Lg(this.options,n),d={queryKey:this.queryKey,meta:this.meta};return o(d),G(this,rr,!1),this.options.persister?this.options.persister(p,d,this):p(d)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};o(i),(c=this.options.behavior)==null||c.onFetch(i,this),G(this,Kr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=i.fetchOptions)==null?void 0:u.meta))&&Ae(this,St,Wt).call(this,{type:"fetch",meta:(f=i.fetchOptions)==null?void 0:f.meta});const a=p=>{var d,x,w,y;cl(p)&&p.silent||Ae(this,St,Wt).call(this,{type:"error",error:p}),cl(p)||((x=(d=P(this,ut).config).onError)==null||x.call(d,p,this),(y=(w=P(this,ut).config).onSettled)==null||y.call(w,this.state.data,p,this)),this.scheduleGc()};return G(this,Ie,zg({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:p=>{var d,x,w,y;if(p===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(C){a(C);return}(x=(d=P(this,ut).config).onSuccess)==null||x.call(d,p,this),(y=(w=P(this,ut).config).onSettled)==null||y.call(w,p,this.state.error,this),this.scheduleGc()},onError:a,onFail:(p,d)=>{Ae(this,St,Wt).call(this,{type:"failed",failureCount:p,error:d})},onPause:()=>{Ae(this,St,Wt).call(this,{type:"pause"})},onContinue:()=>{Ae(this,St,Wt).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),P(this,Ie).start()}},Qr=new WeakMap,Kr=new WeakMap,ut=new WeakMap,Ie=new WeakMap,hs=new WeakMap,rr=new WeakMap,St=new WeakSet,Wt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...kC(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return cl(o)&&o.revert&&P(this,Kr)?{...P(this,Kr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Be.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),P(this,ut).notify({query:this,type:"updated",action:t})})},Kf);function kC(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Dg(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function PC(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Lt,qf,TC=(qf=class extends Ca{constructor(t={}){super();J(this,Lt);this.config=t,G(this,Lt,new Map)}build(t,n,r){const o=n.queryKey,s=n.queryHash??Ru(o,n);let i=this.get(s);return i||(i=new jC({cache:this,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){P(this,Lt).has(t.queryHash)||(P(this,Lt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=P(this,Lt).get(t.queryHash);n&&(t.destroy(),n===t&&P(this,Lt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return P(this,Lt).get(t)}getAll(){return[...P(this,Lt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Tf(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Tf(t,r)):n}notify(t){Be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Be.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Be.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Lt=new WeakMap,qf),It,ze,or,Dt,pn,Yf,RC=(Yf=class extends $g{constructor(t){super();J(this,Dt);J(this,It);J(this,ze);J(this,or);this.mutationId=t.mutationId,G(this,ze,t.mutationCache),G(this,It,[]),this.state=t.state||_C(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){P(this,It).includes(t)||(P(this,It).push(t),this.clearGcTimeout(),P(this,ze).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){G(this,It,P(this,It).filter(n=>n!==t)),this.scheduleGc(),P(this,ze).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){P(this,It).length||(this.state.status==="pending"?this.scheduleGc():P(this,ze).remove(this))}continue(){var t;return((t=P(this,or))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,s,i,a,c,u,f,p,d,x,w,y,C,m,h,v,S,N,b,k;G(this,or,zg({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(j,R)=>{Ae(this,Dt,pn).call(this,{type:"failed",failureCount:j,error:R})},onPause:()=>{Ae(this,Dt,pn).call(this,{type:"pause"})},onContinue:()=>{Ae(this,Dt,pn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>P(this,ze).canRun(this)}));const n=this.state.status==="pending",r=!P(this,or).canStart();try{if(!n){Ae(this,Dt,pn).call(this,{type:"pending",variables:t,isPaused:r}),await((s=(o=P(this,ze).config).onMutate)==null?void 0:s.call(o,t,this));const R=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,t));R!==this.state.context&&Ae(this,Dt,pn).call(this,{type:"pending",context:R,variables:t,isPaused:r})}const j=await P(this,or).start();return await((u=(c=P(this,ze).config).onSuccess)==null?void 0:u.call(c,j,t,this.state.context,this)),await((p=(f=this.options).onSuccess)==null?void 0:p.call(f,j,t,this.state.context)),await((x=(d=P(this,ze).config).onSettled)==null?void 0:x.call(d,j,null,this.state.variables,this.state.context,this)),await((y=(w=this.options).onSettled)==null?void 0:y.call(w,j,null,t,this.state.context)),Ae(this,Dt,pn).call(this,{type:"success",data:j}),j}catch(j){try{throw await((m=(C=P(this,ze).config).onError)==null?void 0:m.call(C,j,t,this.state.context,this)),await((v=(h=this.options).onError)==null?void 0:v.call(h,j,t,this.state.context)),await((N=(S=P(this,ze).config).onSettled)==null?void 0:N.call(S,void 0,j,this.state.variables,this.state.context,this)),await((k=(b=this.options).onSettled)==null?void 0:k.call(b,void 0,j,t,this.state.context)),j}finally{Ae(this,Dt,pn).call(this,{type:"error",error:j})}}finally{P(this,ze).runNext(this)}}},It=new WeakMap,ze=new WeakMap,or=new WeakMap,Dt=new WeakSet,pn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Be.batch(()=>{P(this,It).forEach(r=>{r.onMutationUpdate(t)}),P(this,ze).notify({mutation:this,type:"updated",action:t})})},Yf);function _C(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Je,ms,Xf,OC=(Xf=class extends Ca{constructor(t={}){super();J(this,Je);J(this,ms);this.config=t,G(this,Je,new Map),G(this,ms,Date.now())}build(t,n,r){const o=new RC({mutationCache:this,mutationId:++Ts(this,ms)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=Xs(t),r=P(this,Je).get(n)??[];r.push(t),P(this,Je).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Xs(t);if(P(this,Je).has(n)){const o=(r=P(this,Je).get(n))==null?void 0:r.filter(s=>s!==t);o&&(o.length===0?P(this,Je).delete(n):P(this,Je).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=P(this,Je).get(Xs(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=P(this,Je).get(Xs(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...P(this,Je).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Rf(n,r))}findAll(t={}){return this.getAll().filter(n=>Rf(t,n))}notify(t){Be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Be.batch(()=>Promise.all(t.map(n=>n.continue().catch(Ct))))}},Je=new WeakMap,ms=new WeakMap,Xf);function Xs(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Af(e){return{onFetch:(t,n)=>{var f,p,d,x,w;const r=t.options,o=(d=(p=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:p.fetchMore)==null?void 0:d.direction,s=((x=t.state.data)==null?void 0:x.pages)||[],i=((w=t.state.data)==null?void 0:w.pageParams)||[];let a={pages:[],pageParams:[]},c=0;const u=async()=>{let y=!1;const C=v=>{Object.defineProperty(v,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},m=Lg(t.options,t.fetchOptions),h=async(v,S,N)=>{if(y)return Promise.reject();if(S==null&&v.pages.length)return Promise.resolve(v);const b={queryKey:t.queryKey,pageParam:S,direction:N?"backward":"forward",meta:t.options.meta};C(b);const k=await m(b),{maxPages:j}=t.options,R=N?wC:xC;return{pages:R(v.pages,k,j),pageParams:R(v.pageParams,S,j)}};if(o&&s.length){const v=o==="backward",S=v?AC:Mf,N={pages:s,pageParams:i},b=S(r,N);a=await h(N,b,v)}else{const v=e??s.length;do{const S=c===0?i[0]??r.initialPageParam:Mf(r,a);if(c>0&&S==null)break;a=await h(a,S),c++}while(c<v)}return a};t.options.persister?t.fetchFn=()=>{var y,C;return(C=(y=t.options).persister)==null?void 0:C.call(y,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Mf(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function AC(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var ge,Cn,Sn,qr,Yr,Nn,Xr,Zr,Zf,MC=(Zf=class{constructor(e={}){J(this,ge);J(this,Cn);J(this,Sn);J(this,qr);J(this,Yr);J(this,Nn);J(this,Xr);J(this,Zr);G(this,ge,e.queryCache||new TC),G(this,Cn,e.mutationCache||new OC),G(this,Sn,e.defaultOptions||{}),G(this,qr,new Map),G(this,Yr,new Map),G(this,Nn,0)}mount(){Ts(this,Nn)._++,P(this,Nn)===1&&(G(this,Xr,Ig.subscribe(async e=>{e&&(await this.resumePausedMutations(),P(this,ge).onFocus())})),G(this,Zr,Vi.subscribe(async e=>{e&&(await this.resumePausedMutations(),P(this,ge).onOnline())})))}unmount(){var e,t;Ts(this,Nn)._--,P(this,Nn)===0&&((e=P(this,Xr))==null||e.call(this),G(this,Xr,void 0),(t=P(this,Zr))==null||t.call(this),G(this,Zr,void 0))}isFetching(e){return P(this,ge).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return P(this,Cn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=P(this,ge).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=P(this,ge).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Pf(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return P(this,ge).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=P(this,ge).get(r.queryHash),s=o==null?void 0:o.state.data,i=pC(t,s);if(i!==void 0)return P(this,ge).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Be.batch(()=>P(this,ge).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=P(this,ge).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=P(this,ge);Be.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=P(this,ge),r={type:"active",...e};return Be.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Be.batch(()=>P(this,ge).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(Ct).catch(Ct)}invalidateQueries(e={},t={}){return Be.batch(()=>{if(P(this,ge).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Be.batch(()=>P(this,ge).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let s=o.fetch(void 0,n);return n.throwOnError||(s=s.catch(Ct)),o.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(Ct)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=P(this,ge).build(this,t);return n.isStaleByTime(Pf(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Ct).catch(Ct)}fetchInfiniteQuery(e){return e.behavior=Af(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Ct).catch(Ct)}ensureInfiniteQueryData(e){return e.behavior=Af(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Vi.isOnline()?P(this,Cn).resumePausedMutations():Promise.resolve()}getQueryCache(){return P(this,ge)}getMutationCache(){return P(this,Cn)}getDefaultOptions(){return P(this,Sn)}setDefaultOptions(e){G(this,Sn,e)}setQueryDefaults(e,t){P(this,qr).set(us(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...P(this,qr).values()];let n={};return t.forEach(r=>{ds(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){P(this,Yr).set(us(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...P(this,Yr).values()];let n={};return t.forEach(r=>{ds(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...P(this,Sn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Ru(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===_u&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...P(this,Sn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){P(this,ge).clear(),P(this,Cn).clear()}},ge=new WeakMap,Cn=new WeakMap,Sn=new WeakMap,qr=new WeakMap,Yr=new WeakMap,Nn=new WeakMap,Xr=new WeakMap,Zr=new WeakMap,Zf),LC=g.createContext(void 0),IC=({client:e,children:t})=>(g.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),l.jsx(LC.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function fs(){return fs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},fs.apply(this,arguments)}var jn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(jn||(jn={}));const Lf="popstate";function DC(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:i,hash:a}=r.location;return vc("",{pathname:s,search:i,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Hi(o)}return zC(t,n,null,e)}function we(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Bg(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function FC(){return Math.random().toString(36).substr(2,8)}function If(e,t){return{usr:e.state,key:e.key,idx:t}}function vc(e,t,n,r){return n===void 0&&(n=null),fs({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?mo(t):t,{state:n,key:t&&t.key||r||FC()})}function Hi(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function mo(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function zC(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,i=o.history,a=jn.Pop,c=null,u=f();u==null&&(u=0,i.replaceState(fs({},i.state,{idx:u}),""));function f(){return(i.state||{idx:null}).idx}function p(){a=jn.Pop;let C=f(),m=C==null?null:C-u;u=C,c&&c({action:a,location:y.location,delta:m})}function d(C,m){a=jn.Push;let h=vc(y.location,C,m);u=f()+1;let v=If(h,u),S=y.createHref(h);try{i.pushState(v,"",S)}catch(N){if(N instanceof DOMException&&N.name==="DataCloneError")throw N;o.location.assign(S)}s&&c&&c({action:a,location:y.location,delta:1})}function x(C,m){a=jn.Replace;let h=vc(y.location,C,m);u=f();let v=If(h,u),S=y.createHref(h);i.replaceState(v,"",S),s&&c&&c({action:a,location:y.location,delta:0})}function w(C){let m=o.location.origin!=="null"?o.location.origin:o.location.href,h=typeof C=="string"?C:Hi(C);return h=h.replace(/ $/,"%20"),we(m,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,m)}let y={get action(){return a},get location(){return e(o,i)},listen(C){if(c)throw new Error("A history only accepts one active listener");return o.addEventListener(Lf,p),c=C,()=>{o.removeEventListener(Lf,p),c=null}},createHref(C){return t(o,C)},createURL:w,encodeLocation(C){let m=w(C);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:d,replace:x,go(C){return i.go(C)}};return y}var Df;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Df||(Df={}));function $C(e,t,n){return n===void 0&&(n="/"),BC(e,t,n,!1)}function BC(e,t,n,r){let o=typeof t=="string"?mo(t):t,s=Ou(o.pathname||"/",n);if(s==null)return null;let i=Ug(e);UC(i);let a=null;for(let c=0;a==null&&c<i.length;++c){let u=JC(s);a=XC(i[c],u,r)}return a}function Ug(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,i,a)=>{let c={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:i,route:s};c.relativePath.startsWith("/")&&(we(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let u=Ln([r,c.relativePath]),f=n.concat(c);s.children&&s.children.length>0&&(we(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Ug(s.children,t,f,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:qC(u,s.index),routesMeta:f})};return e.forEach((s,i)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))o(s,i);else for(let c of Vg(s.path))o(s,i,c)}),t}function Vg(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let i=Vg(r.join("/")),a=[];return a.push(...i.map(c=>c===""?s:[s,c].join("/"))),o&&a.push(...i),a.map(c=>e.startsWith("/")&&c===""?"/":c)}function UC(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:YC(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const VC=/^:[\w-]+$/,HC=3,WC=2,GC=1,QC=10,KC=-2,Ff=e=>e==="*";function qC(e,t){let n=e.split("/"),r=n.length;return n.some(Ff)&&(r+=KC),t&&(r+=WC),n.filter(o=>!Ff(o)).reduce((o,s)=>o+(VC.test(s)?HC:s===""?GC:QC),r)}function YC(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function XC(e,t,n){let{routesMeta:r}=e,o={},s="/",i=[];for(let a=0;a<r.length;++a){let c=r[a],u=a===r.length-1,f=s==="/"?t:t.slice(s.length)||"/",p=zf({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},f),d=c.route;if(!p&&u&&n&&!r[r.length-1].route.index&&(p=zf({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},f)),!p)return null;Object.assign(o,p.params),i.push({params:o,pathname:Ln([s,p.pathname]),pathnameBase:rS(Ln([s,p.pathnameBase])),route:d}),p.pathnameBase!=="/"&&(s=Ln([s,p.pathnameBase]))}return i}function zf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ZC(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],i=s.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,f,p)=>{let{paramName:d,isOptional:x}=f;if(d==="*"){let y=a[p]||"";i=s.slice(0,s.length-y.length).replace(/(.)\/+$/,"$1")}const w=a[p];return x&&!w?u[d]=void 0:u[d]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:i,pattern:e}}function ZC(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Bg(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,c)=>(r.push({paramName:a,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function JC(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Bg(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Ou(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function eS(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?mo(e):e;return{pathname:n?n.startsWith("/")?n:tS(n,t):t,search:oS(r),hash:sS(o)}}function tS(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function ul(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function nS(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Hg(e,t){let n=nS(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Wg(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=mo(e):(o=fs({},e),we(!o.pathname||!o.pathname.includes("?"),ul("?","pathname","search",o)),we(!o.pathname||!o.pathname.includes("#"),ul("#","pathname","hash",o)),we(!o.search||!o.search.includes("#"),ul("#","search","hash",o)));let s=e===""||o.pathname==="",i=s?"/":o.pathname,a;if(i==null)a=n;else{let p=t.length-1;if(!r&&i.startsWith("..")){let d=i.split("/");for(;d[0]==="..";)d.shift(),p-=1;o.pathname=d.join("/")}a=p>=0?t[p]:"/"}let c=eS(o,a),u=i&&i!=="/"&&i.endsWith("/"),f=(s||i===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||f)&&(c.pathname+="/"),c}const Ln=e=>e.join("/").replace(/\/\/+/g,"/"),rS=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),oS=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,sS=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function iS(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Gg=["post","put","patch","delete"];new Set(Gg);const aS=["get",...Gg];new Set(aS);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ps(){return ps=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ps.apply(this,arguments)}const Au=g.createContext(null),lS=g.createContext(null),vr=g.createContext(null),Na=g.createContext(null),Kn=g.createContext({outlet:null,matches:[],isDataRoute:!1}),Qg=g.createContext(null);function cS(e,t){let{relative:n}=t===void 0?{}:t;Es()||we(!1);let{basename:r,navigator:o}=g.useContext(vr),{hash:s,pathname:i,search:a}=qg(e,{relative:n}),c=i;return r!=="/"&&(c=i==="/"?r:Ln([r,i])),o.createHref({pathname:c,search:a,hash:s})}function Es(){return g.useContext(Na)!=null}function go(){return Es()||we(!1),g.useContext(Na).location}function Kg(e){g.useContext(vr).static||g.useLayoutEffect(e)}function uS(){let{isDataRoute:e}=g.useContext(Kn);return e?NS():dS()}function dS(){Es()||we(!1);let e=g.useContext(Au),{basename:t,future:n,navigator:r}=g.useContext(vr),{matches:o}=g.useContext(Kn),{pathname:s}=go(),i=JSON.stringify(Hg(o,n.v7_relativeSplatPath)),a=g.useRef(!1);return Kg(()=>{a.current=!0}),g.useCallback(function(u,f){if(f===void 0&&(f={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let p=Wg(u,JSON.parse(i),s,f.relative==="path");e==null&&t!=="/"&&(p.pathname=p.pathname==="/"?t:Ln([t,p.pathname])),(f.replace?r.replace:r.push)(p,f.state,f)},[t,r,i,s,e])}function Mu(){let{matches:e}=g.useContext(Kn),t=e[e.length-1];return t?t.params:{}}function qg(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=g.useContext(vr),{matches:o}=g.useContext(Kn),{pathname:s}=go(),i=JSON.stringify(Hg(o,r.v7_relativeSplatPath));return g.useMemo(()=>Wg(e,JSON.parse(i),s,n==="path"),[e,i,s,n])}function fS(e,t){return pS(e,t)}function pS(e,t,n,r){Es()||we(!1);let{navigator:o}=g.useContext(vr),{matches:s}=g.useContext(Kn),i=s[s.length-1],a=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let u=go(),f;if(t){var p;let C=typeof t=="string"?mo(t):t;c==="/"||(p=C.pathname)!=null&&p.startsWith(c)||we(!1),f=C}else f=u;let d=f.pathname||"/",x=d;if(c!=="/"){let C=c.replace(/^\//,"").split("/");x="/"+d.replace(/^\//,"").split("/").slice(C.length).join("/")}let w=$C(e,{pathname:x}),y=yS(w&&w.map(C=>Object.assign({},C,{params:Object.assign({},a,C.params),pathname:Ln([c,o.encodeLocation?o.encodeLocation(C.pathname).pathname:C.pathname]),pathnameBase:C.pathnameBase==="/"?c:Ln([c,o.encodeLocation?o.encodeLocation(C.pathnameBase).pathname:C.pathnameBase])})),s,n,r);return t&&y?g.createElement(Na.Provider,{value:{location:ps({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:jn.Pop}},y):y}function hS(){let e=SS(),t=iS(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),n?g.createElement("pre",{style:o},n):null,null)}const mS=g.createElement(hS,null);class gS extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?g.createElement(Kn.Provider,{value:this.props.routeContext},g.createElement(Qg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function vS(e){let{routeContext:t,match:n,children:r}=e,o=g.useContext(Au);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),g.createElement(Kn.Provider,{value:t},r)}function yS(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let f=i.findIndex(p=>p.route.id&&(a==null?void 0:a[p.route.id])!==void 0);f>=0||we(!1),i=i.slice(0,Math.min(i.length,f+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<i.length;f++){let p=i[f];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(u=f),p.route.id){let{loaderData:d,errors:x}=n,w=p.route.loader&&d[p.route.id]===void 0&&(!x||x[p.route.id]===void 0);if(p.route.lazy||w){c=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((f,p,d)=>{let x,w=!1,y=null,C=null;n&&(x=a&&p.route.id?a[p.route.id]:void 0,y=p.route.errorElement||mS,c&&(u<0&&d===0?(w=!0,C=null):u===d&&(w=!0,C=p.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,d+1)),h=()=>{let v;return x?v=y:w?v=C:p.route.Component?v=g.createElement(p.route.Component,null):p.route.element?v=p.route.element:v=f,g.createElement(vS,{match:p,routeContext:{outlet:f,matches:m,isDataRoute:n!=null},children:v})};return n&&(p.route.ErrorBoundary||p.route.errorElement||d===0)?g.createElement(gS,{location:n.location,revalidation:n.revalidation,component:y,error:x,children:h(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):h()},null)}var Yg=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Yg||{}),Wi=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Wi||{});function xS(e){let t=g.useContext(Au);return t||we(!1),t}function wS(e){let t=g.useContext(lS);return t||we(!1),t}function CS(e){let t=g.useContext(Kn);return t||we(!1),t}function Xg(e){let t=CS(),n=t.matches[t.matches.length-1];return n.route.id||we(!1),n.route.id}function SS(){var e;let t=g.useContext(Qg),n=wS(Wi.UseRouteError),r=Xg(Wi.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function NS(){let{router:e}=xS(Yg.UseNavigateStable),t=Xg(Wi.UseNavigateStable),n=g.useRef(!1);return Kg(()=>{n.current=!0}),g.useCallback(function(o,s){s===void 0&&(s={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,ps({fromRouteId:t},s)))},[e,t])}function hn(e){we(!1)}function ES(e){let{basename:t="/",children:n=null,location:r,navigationType:o=jn.Pop,navigator:s,static:i=!1,future:a}=e;Es()&&we(!1);let c=t.replace(/^\/*/,"/"),u=g.useMemo(()=>({basename:c,navigator:s,static:i,future:ps({v7_relativeSplatPath:!1},a)}),[c,a,s,i]);typeof r=="string"&&(r=mo(r));let{pathname:f="/",search:p="",hash:d="",state:x=null,key:w="default"}=r,y=g.useMemo(()=>{let C=Ou(f,c);return C==null?null:{location:{pathname:C,search:p,hash:d,state:x,key:w},navigationType:o}},[c,f,p,d,x,w,o]);return y==null?null:g.createElement(vr.Provider,{value:u},g.createElement(Na.Provider,{children:n,value:y}))}function bS(e){let{children:t,location:n}=e;return fS(yc(t),n)}new Promise(()=>{});function yc(e,t){t===void 0&&(t=[]);let n=[];return g.Children.forEach(e,(r,o)=>{if(!g.isValidElement(r))return;let s=[...t,o];if(r.type===g.Fragment){n.push.apply(n,yc(r.props.children,s));return}r.type!==hn&&we(!1),!r.props.index||!r.props.children||we(!1);let i={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=yc(r.props.children,s)),n.push(i)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function xc(){return xc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xc.apply(this,arguments)}function jS(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function kS(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function PS(e,t){return e.button===0&&(!t||t==="_self")&&!kS(e)}const TS=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],RS="6";try{window.__reactRouterVersion=RS}catch{}const _S="startTransition",$f=up[_S];function OS(e){let{basename:t,children:n,future:r,window:o}=e,s=g.useRef();s.current==null&&(s.current=DC({window:o,v5Compat:!0}));let i=s.current,[a,c]=g.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},f=g.useCallback(p=>{u&&$f?$f(()=>c(p)):c(p)},[c,u]);return g.useLayoutEffect(()=>i.listen(f),[i,f]),g.createElement(ES,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:i,future:r})}const AS=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",MS=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,vt=g.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:s,replace:i,state:a,target:c,to:u,preventScrollReset:f,viewTransition:p}=t,d=jS(t,TS),{basename:x}=g.useContext(vr),w,y=!1;if(typeof u=="string"&&MS.test(u)&&(w=u,AS))try{let v=new URL(window.location.href),S=u.startsWith("//")?new URL(v.protocol+u):new URL(u),N=Ou(S.pathname,x);S.origin===v.origin&&N!=null?u=N+S.search+S.hash:y=!0}catch{}let C=cS(u,{relative:o}),m=LS(u,{replace:i,state:a,target:c,preventScrollReset:f,relative:o,viewTransition:p});function h(v){r&&r(v),v.defaultPrevented||m(v)}return g.createElement("a",xc({},d,{href:w||C,onClick:y||s?r:h,ref:n,target:c}))});var Bf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Bf||(Bf={}));var Uf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Uf||(Uf={}));function LS(e,t){let{target:n,replace:r,state:o,preventScrollReset:s,relative:i,viewTransition:a}=t===void 0?{}:t,c=uS(),u=go(),f=qg(e,{relative:i});return g.useCallback(p=>{if(PS(p,n)){p.preventDefault();let d=r!==void 0?r:Hi(u)===Hi(f);c(e,{replace:d,state:o,preventScrollReset:s,relative:i,viewTransition:a})}},[u,c,f,r,o,n,e,s,i,a])}const IS=ha("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),X=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?ss:"button";return l.jsx(i,{className:me(IS({variant:t,size:n,className:e})),ref:s,...o})});X.displayName="Button";const ke=g.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:me("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));ke.displayName="Card";const Pe=g.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:me("flex flex-col space-y-1.5 p-6",e),...t}));Pe.displayName="CardHeader";const Te=g.forwardRef(({className:e,...t},n)=>l.jsx("h3",{ref:n,className:me("text-2xl font-semibold leading-none tracking-tight",e),...t}));Te.displayName="CardTitle";const Ut=g.forwardRef(({className:e,...t},n)=>l.jsx("p",{ref:n,className:me("text-sm text-muted-foreground",e),...t}));Ut.displayName="CardDescription";const Re=g.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:me("p-6 pt-0",e),...t}));Re.displayName="CardContent";const DS=g.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:me("flex items-center p-6 pt-0",e),...t}));DS.displayName="CardFooter";const Hn=({children:e})=>{const t=go(),n=[{name:"Dashboard",href:"/",icon:f1},{name:"Templates",href:"/templates",icon:as},{name:"Documents",href:"/documents",icon:g1}];return l.jsxs("div",{className:"min-h-screen bg-gray-50",children:[l.jsx("header",{className:"bg-white shadow-sm border-b",children:l.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"flex justify-between items-center h-16",children:[l.jsxs("div",{className:"flex items-center space-x-8",children:[l.jsxs(vt,{to:"/",className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-8 h-8 bg-[#2C3E50] rounded-lg flex items-center justify-center",children:l.jsx(as,{className:"w-5 h-5 text-white"})}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-lg font-bold text-[#2C3E50]",children:"Premium SOP Templates"}),l.jsx("p",{className:"text-xs text-gray-500",children:"Compliance Made Simple"})]})]}),l.jsx("nav",{className:"hidden md:flex space-x-6",children:n.map(r=>{const o=r.icon,s=t.pathname===r.href;return l.jsxs(vt,{to:r.href,className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${s?"bg-[#3498DB] text-white":"text-gray-600 hover:text-[#2C3E50] hover:bg-gray-100"}`,children:[l.jsx(o,{className:"w-4 h-4"}),l.jsx("span",{children:r.name})]},r.name)})})]}),l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(X,{variant:"ghost",size:"sm",children:l.jsx(Km,{className:"w-4 h-4"})}),l.jsx(X,{variant:"ghost",size:"sm",children:l.jsx(S1,{className:"w-4 h-4"})})]})]})})}),l.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e})]})},FS=()=>{const e=[{id:"restaurant",title:"Restaurant Operations",description:"Food safety, HACCP compliance, staff training procedures",icon:"🍽️",count:12,compliance:["FDA Food Code","HACCP","ServSafe"]},{id:"healthcare",title:"Healthcare Procedures",description:"Patient care protocols, safety procedures, HIPAA compliance",icon:"🏥",count:18,compliance:["HIPAA","CDC Guidelines","Joint Commission"]},{id:"it-onboarding",title:"IT Onboarding",description:"Employee setup, security protocols, system access procedures",icon:"💻",count:8,compliance:["SOX","ISO 27001","GDPR"]},{id:"customer-service",title:"Customer Service",description:"Support procedures, escalation protocols, quality standards",icon:"📞",count:10,compliance:["ISO 9001","CSAT Standards"]}],t=[{provider:"Groq",status:"operational",model:"llama-3.1-70b-versatile"},{provider:"Hugging Face",status:"operational",model:"Various OSS Models"},{provider:"Together AI",status:"operational",model:"Free Tier Active"},{provider:"OpenRouter",status:"operational",model:"deepseek-chat"}];return l.jsxs(Hn,{children:[l.jsxs("div",{className:"text-center mb-12",children:[l.jsx("h1",{className:"text-4xl font-bold text-[#2C3E50] mb-4",children:"Professional SOP Templates"}),l.jsx("p",{className:"text-xl text-gray-600 mb-6 max-w-3xl mx-auto",children:"Generate industry-compliant Standard Operating Procedures using free AI providers. Professional formatting, regulatory compliance, and zero subscription costs."}),l.jsxs("div",{className:"flex justify-center space-x-4",children:[l.jsx(X,{asChild:!0,size:"lg",className:"bg-[#3498DB] hover:bg-[#2980B9]",children:l.jsxs(vt,{to:"/templates",children:["Browse Templates ",l.jsx(c1,{className:"ml-2 w-4 h-4"})]})}),l.jsx(X,{variant:"outline",size:"lg",children:"View Documentation"})]})]}),l.jsxs("div",{className:"mb-12",children:[l.jsx("h2",{className:"text-2xl font-bold text-[#2C3E50] mb-6",children:"Template Categories"}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map(n=>l.jsx(ke,{className:"hover:shadow-lg transition-shadow cursor-pointer group",children:l.jsxs(vt,{to:`/generate/${n.id}`,children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx("div",{className:"text-4xl mb-2",children:n.icon}),l.jsx(Te,{className:"text-lg group-hover:text-[#3498DB] transition-colors",children:n.title}),l.jsx(Ut,{className:"text-sm",children:n.description})]}),l.jsx(Re,{children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-[#3498DB] mb-2",children:n.count}),l.jsx("div",{className:"text-xs text-gray-500 mb-3",children:"Templates Available"}),l.jsx("div",{className:"flex flex-wrap gap-1",children:n.compliance.map(r=>l.jsx("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded",children:r},r))})]})})]})},n.id))})]}),l.jsxs("div",{className:"mb-12",children:[l.jsx("h2",{className:"text-2xl font-bold text-[#2C3E50] mb-6",children:"Key Features"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[l.jsxs(ke,{children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx(ar,{className:"w-8 h-8 text-[#E74C3C] mx-auto mb-2"}),l.jsx(Te,{className:"text-lg",children:"100% Free AI"})]}),l.jsx(Re,{children:l.jsx("p",{className:"text-sm text-gray-600 text-center",children:"Multiple free LLM providers with automatic fallback ensure zero costs"})})]}),l.jsxs(ke,{children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx(as,{className:"w-8 h-8 text-[#E74C3C] mx-auto mb-2"}),l.jsx(Te,{className:"text-lg",children:"Professional PDFs"})]}),l.jsx(Re,{children:l.jsx("p",{className:"text-sm text-gray-600 text-center",children:"Beautifully formatted documents with proper typography and branding"})})]}),l.jsxs(ke,{children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx(x1,{className:"w-8 h-8 text-[#E74C3C] mx-auto mb-2"}),l.jsx(Te,{className:"text-lg",children:"Compliance Ready"})]}),l.jsx(Re,{children:l.jsx("p",{className:"text-sm text-gray-600 text-center",children:"Industry-specific compliance standards and regulatory citations"})})]}),l.jsxs(ke,{children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx(N1,{className:"w-8 h-8 text-[#E74C3C] mx-auto mb-2"}),l.jsx(Te,{className:"text-lg",children:"Team Ready"})]}),l.jsx(Re,{children:l.jsx("p",{className:"text-sm text-gray-600 text-center",children:"Multiple user support with role-based access and collaboration"})})]})]})]}),l.jsxs("div",{className:"mb-8",children:[l.jsx("h2",{className:"text-2xl font-bold text-[#2C3E50] mb-6",children:"System Status"}),l.jsxs(ke,{children:[l.jsxs(Pe,{children:[l.jsxs(Te,{className:"flex items-center",children:[l.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),"All Systems Operational"]}),l.jsx(Ut,{children:"All LLM providers are functioning normally. Average response time: 2.3 seconds"})]}),l.jsx(Re,{children:l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:t.map(n=>l.jsxs("div",{className:"border rounded-lg p-3",children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx("span",{className:"font-medium",children:n.provider}),l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]}),l.jsx("div",{className:"text-xs text-gray-600",children:n.model})]},n.provider))})})]})]})]})},Ge=g.forwardRef(({className:e,type:t,...n},r)=>l.jsx("input",{type:t,className:me("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));Ge.displayName="Input";const zS=ha("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function hr({className:e,variant:t,...n}){return l.jsx("div",{className:me(zS({variant:t}),e),...n})}const $S=()=>{const[e,t]=g.useState(""),[n,r]=g.useState("all"),o=[{id:"all",name:"All Templates",count:48},{id:"restaurant",name:"Restaurant",count:12},{id:"healthcare",name:"Healthcare",count:18},{id:"it-onboarding",name:"IT Onboarding",count:8},{id:"customer-service",name:"Customer Service",count:10}],s=[{id:"restaurant-opening",title:"Restaurant Opening Procedures",description:"Complete checklist for daily restaurant opening procedures including equipment checks, food safety, and staff preparation.",category:"restaurant",compliance:["FDA Food Code","HACCP","ServSafe"],rating:4.8,downloads:1247,estimatedTime:"2-3 minutes",icon:"🍽️"},{id:"healthcare-patient-intake",title:"Patient Intake Procedures",description:"Comprehensive patient intake process including registration, insurance verification, and HIPAA compliance procedures.",category:"healthcare",compliance:["HIPAA","CDC Guidelines","Joint Commission"],rating:4.9,downloads:2134,estimatedTime:"3-4 minutes",icon:"🏥"},{id:"it-employee-onboarding",title:"IT Employee Onboarding",description:"Complete IT onboarding process including account setup, security training, and system access procedures.",category:"it-onboarding",compliance:["SOX","ISO 27001","GDPR"],rating:4.7,downloads:856,estimatedTime:"4-5 minutes",icon:"💻"},{id:"customer-service-escalation",title:"Customer Service Escalation",description:"Escalation procedures for customer service issues including severity levels and manager notification protocols.",category:"customer-service",compliance:["ISO 9001","CSAT Standards"],rating:4.6,downloads:934,estimatedTime:"2-3 minutes",icon:"📞"},{id:"restaurant-cleaning",title:"Deep Cleaning Procedures",description:"Detailed cleaning and sanitization procedures for restaurant equipment, surfaces, and dining areas.",category:"restaurant",compliance:["FDA Food Code","Local Health Dept"],rating:4.8,downloads:1156,estimatedTime:"3-4 minutes",icon:"🧽"},{id:"healthcare-emergency",title:"Medical Emergency Response",description:"Emergency response procedures for medical facilities including code team activation and patient stabilization.",category:"healthcare",compliance:["Joint Commission","AHA Guidelines"],rating:4.9,downloads:1678,estimatedTime:"3-4 minutes",icon:"🚨"}],i=s.filter(a=>{const c=a.title.toLowerCase().includes(e.toLowerCase())||a.description.toLowerCase().includes(e.toLowerCase()),u=n==="all"||a.category===n;return c&&u});return l.jsx(Hn,{children:l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-[#2C3E50] mb-2",children:"SOP Template Library"}),l.jsx("p",{className:"text-gray-600",children:"Choose from our professionally crafted SOP templates, all with built-in compliance standards."})]}),l.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[l.jsxs("div",{className:"relative flex-1",children:[l.jsx(Qm,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx(Ge,{placeholder:"Search templates...",value:e,onChange:a=>t(a.target.value),className:"pl-10"})]}),l.jsxs(X,{variant:"outline",className:"md:w-auto",children:[l.jsx(m1,{className:"w-4 h-4 mr-2"}),"Advanced Filters"]})]}),l.jsx("div",{className:"flex flex-wrap gap-2",children:o.map(a=>l.jsxs(X,{variant:n===a.id?"default":"outline",onClick:()=>r(a.id),className:`${n===a.id?"bg-[#3498DB] hover:bg-[#2980B9]":""}`,children:[a.name," (",a.count,")"]},a.id))}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map(a=>l.jsxs(ke,{className:"hover:shadow-lg transition-shadow",children:[l.jsxs(Pe,{children:[l.jsxs("div",{className:"flex items-start justify-between",children:[l.jsx("div",{className:"text-3xl mb-2",children:a.icon}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(hf,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"}),l.jsx("span",{className:"text-sm font-medium",children:a.rating})]})]}),l.jsx(Te,{className:"text-lg leading-tight",children:a.title}),l.jsx(Ut,{className:"text-sm",children:a.description})]}),l.jsxs(Re,{className:"space-y-4",children:[l.jsx("div",{className:"flex flex-wrap gap-1",children:a.compliance.map(c=>l.jsx(hr,{variant:"secondary",className:"text-xs",children:c},c))}),l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(ma,{className:"w-4 h-4"}),l.jsxs("span",{children:[a.downloads.toLocaleString()," downloads"]})]}),l.jsxs("span",{children:["Est. ",a.estimatedTime]})]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx(X,{asChild:!0,className:"flex-1 bg-[#3498DB] hover:bg-[#2980B9]",children:l.jsx(vt,{to:`/generate/${a.id}`,children:"Generate SOP"})}),l.jsx(X,{variant:"outline",size:"icon",children:l.jsx(hf,{className:"w-4 h-4"})})]})]})]},a.id))}),l.jsxs("div",{className:"text-center text-gray-600",children:["Showing ",i.length," of ",s.length," templates"]})]})})};var BS="Label",Zg=g.forwardRef((e,t)=>l.jsx(ce.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));Zg.displayName=BS;var Jg=Zg;const US=ha("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ue=g.forwardRef(({className:e,...t},n)=>l.jsx(Jg,{ref:n,className:me(US(),e),...t}));Ue.displayName=Jg.displayName;function VS(e){const t=g.useRef({value:e,previous:e});return g.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Lu="Checkbox",[HS,$N]=ca(Lu),[WS,GS]=HS(Lu),ev=g.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:o,defaultChecked:s,required:i,disabled:a,value:c="on",onCheckedChange:u,form:f,...p}=e,[d,x]=g.useState(null),w=at(t,S=>x(S)),y=g.useRef(!1),C=d?f||!!d.closest("form"):!0,[m=!1,h]=ua({prop:o,defaultProp:s,onChange:u}),v=g.useRef(m);return g.useEffect(()=>{const S=d==null?void 0:d.form;if(S){const N=()=>h(v.current);return S.addEventListener("reset",N),()=>S.removeEventListener("reset",N)}},[d,h]),l.jsxs(WS,{scope:n,state:m,disabled:a,children:[l.jsx(ce.button,{type:"button",role:"checkbox","aria-checked":In(m)?"mixed":m,"aria-required":i,"data-state":rv(m),"data-disabled":a?"":void 0,disabled:a,value:c,...p,ref:w,onKeyDown:ee(e.onKeyDown,S=>{S.key==="Enter"&&S.preventDefault()}),onClick:ee(e.onClick,S=>{h(N=>In(N)?!0:!N),C&&(y.current=S.isPropagationStopped(),y.current||S.stopPropagation())})}),C&&l.jsx(QS,{control:d,bubbles:!y.current,name:r,value:c,checked:m,required:i,disabled:a,form:f,style:{transform:"translateX(-100%)"},defaultChecked:In(s)?!1:s})]})});ev.displayName=Lu;var tv="CheckboxIndicator",nv=g.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...o}=e,s=GS(tv,n);return l.jsx(Ss,{present:r||In(s.state)||s.state===!0,children:l.jsx(ce.span,{"data-state":rv(s.state),"data-disabled":s.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});nv.displayName=tv;var QS=e=>{const{control:t,checked:n,bubbles:r=!0,defaultChecked:o,...s}=e,i=g.useRef(null),a=VS(n),c=mg(t);g.useEffect(()=>{const f=i.current,p=window.HTMLInputElement.prototype,x=Object.getOwnPropertyDescriptor(p,"checked").set;if(a!==n&&x){const w=new Event("click",{bubbles:r});f.indeterminate=In(n),x.call(f,In(n)?!1:n),f.dispatchEvent(w)}},[a,n,r]);const u=g.useRef(In(n)?!1:n);return l.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??u.current,...s,tabIndex:-1,ref:i,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function In(e){return e==="indeterminate"}function rv(e){return In(e)?"indeterminate":e?"checked":"unchecked"}var ov=ev,KS=nv;const Iu=g.forwardRef(({className:e,...t},n)=>l.jsx(ov,{ref:n,className:me("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:l.jsx(KS,{className:me("flex items-center justify-center text-current"),children:l.jsx(p1,{className:"h-4 w-4"})})}));Iu.displayName=ov.displayName;function qS(e,t=[]){let n=[];function r(s,i){const a=g.createContext(i),c=n.length;n=[...n,i];function u(p){const{scope:d,children:x,...w}=p,y=(d==null?void 0:d[e][c])||a,C=g.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:C,children:x})}function f(p,d){const x=(d==null?void 0:d[e][c])||a,w=g.useContext(x);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,f]}const o=()=>{const s=n.map(i=>g.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,YS(o,...t)]}function YS(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Du="Progress",Fu=100,[XS,BN]=qS(Du),[ZS,JS]=XS(Du),sv=g.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:s=eN,...i}=e;(o||o===0)&&!Vf(o)&&console.error(tN(`${o}`,"Progress"));const a=Vf(o)?o:Fu;r!==null&&!Hf(r,a)&&console.error(nN(`${r}`,"Progress"));const c=Hf(r,a)?r:null,u=Gi(c)?s(c,a):void 0;return l.jsx(ZS,{scope:n,value:c,max:a,children:l.jsx(ce.div,{"aria-valuemax":a,"aria-valuemin":0,"aria-valuenow":Gi(c)?c:void 0,"aria-valuetext":u,role:"progressbar","data-state":lv(c,a),"data-value":c??void 0,"data-max":a,...i,ref:t})})});sv.displayName=Du;var iv="ProgressIndicator",av=g.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=JS(iv,n);return l.jsx(ce.div,{"data-state":lv(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});av.displayName=iv;function eN(e,t){return`${Math.round(e/t*100)}%`}function lv(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Gi(e){return typeof e=="number"}function Vf(e){return Gi(e)&&!isNaN(e)&&e>0}function Hf(e,t){return Gi(e)&&!isNaN(e)&&e<=t&&e>=0}function tN(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Fu}\`.`}function nN(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Fu} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var cv=sv,rN=av;const zu=g.forwardRef(({className:e,value:t,...n},r)=>l.jsx(cv,{ref:r,className:me("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...n,children:l.jsx(rN,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));zu.displayName=cv.displayName;const oN="https://nextlevelsbs.com";class sN{constructor(t=oN){qu(this,"baseURL");this.baseURL=t}async request(t,n={}){var s;const r=`${this.baseURL}${t}`,o={headers:{"Content-Type":"application/json",...n.headers},...n};try{const i=await fetch(r,o);if(!i.ok){const a=await i.json().catch(()=>({}));throw new Error(((s=a.error)==null?void 0:s.message)||`HTTP ${i.status}: ${i.statusText}`)}return await i.json()}catch(i){throw console.error(`API request failed: ${t}`,i),i}}async healthCheck(){return this.request("/api/health")}async getTemplates(t){const n=t?`?industry=${encodeURIComponent(t)}`:"";return this.request(`/api/v1/templates${n}`)}async getTemplateById(t){return this.request(`/api/v1/templates/${t}`)}async getIndustries(){return this.request("/api/v1/industries")}async startGeneration(t){return this.request("/api/v1/generate",{method:"POST",body:JSON.stringify(t)})}async getGenerationStatus(t){return this.request(`/api/v1/generate/${t}/status`)}async generatePreview(t,n){return this.request("/api/v1/documents/preview",{method:"POST",body:JSON.stringify({template_data:t,brand_config:n,preview_mode:!0})})}async pollGenerationStatus(t,n,r=60,o=2e3){let s=0;return new Promise((i,a)=>{const c=async()=>{try{const u=await this.getGenerationStatus(t);if(n&&n(u),u.status==="completed"){i(u);return}if(u.status==="failed"){a(new Error(u.error||"Generation failed"));return}if(s++,s>=r){a(new Error("Generation timeout"));return}setTimeout(c,o)}catch(u){a(u)}};c()})}}const Nr=new sN;function $u(){return{api:Nr,async generateSOP(e,t,n,r=[],o="automatic"){const s={template_id:e,company_info:{name:t,location:n},customization:{selected_options:r},llm_provider:o},i=await Nr.startGeneration(s);return{generationId:i.generation_id,pollStatus:()=>Nr.pollGenerationStatus(i.generation_id)}},async getTemplatesForIndustry(e){return(await Nr.getTemplates(e)).templates},async getAllTemplates(){return(await Nr.getTemplates()).templates},async checkHealth(){try{return(await Nr.healthCheck()).status==="healthy"}catch{return!1}}}}const iN=()=>{const{templateId:e}=Mu(),{api:t}=$u(),[n,r]=g.useState(null),[o,s]=g.useState(!1),[i,a]=g.useState(0),[c,u]=g.useState(""),[f,p]=g.useState(""),[d,x]=g.useState(""),[w,y]=g.useState([]),[C,m]=g.useState(null),[h,v]=g.useState(null);g.useEffect(()=>{(async()=>{if(e)try{const O=await t.getTemplateById(e);r(O);const $=O.custom_options.filter(T=>T.default).map(T=>T.id);y($)}catch(O){console.error("Failed to load template:",O),v("Failed to load template. Please try again.")}})()},[e,t]);const S=[{id:"automatic",name:"Automatic (Recommended)",description:"Tries Groq → Hugging Face → Together AI → OpenRouter"},{id:"groq",name:"Groq",description:"llama-3.1-70b-versatile (fastest, free tier)"},{id:"huggingface",name:"Hugging Face",description:"300 requests/hour free"},{id:"together",name:"Together AI",description:"$25 free credits"},{id:"openrouter",name:"OpenRouter",description:"deepseek-chat (free tier)"}],[N,b]=g.useState("automatic"),k=R=>{y(O=>O.includes(R)?O.filter($=>$!==R):[...O,R])},j=async()=>{if(!(!n||!f.trim())){s(!0),a(0),u("Starting generation..."),v(null);try{const R=await t.startGeneration({template_id:n.id,company_info:{name:f.trim(),location:d.trim()||void 0},customization:{selected_options:w},llm_provider:N});await t.pollGenerationStatus(R.generation_id,O=>{a(O.progress),u(O.current_step||"Processing..."),O.status==="completed"&&O.result&&(m(O.result),s(!1))})}catch(R){console.error("Generation failed:",R),v(R instanceof Error?R.message:"Generation failed. Please try again."),s(!1),u("")}}};return n?l.jsx(Hn,{children:l.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[l.jsx("div",{className:"flex items-center space-x-4",children:l.jsx(X,{variant:"ghost",asChild:!0,children:l.jsxs(vt,{to:"/templates",children:[l.jsx(is,{className:"w-4 h-4 mr-2"}),"Back to Templates"]})})}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[l.jsx("div",{className:"lg:col-span-1",children:l.jsxs(ke,{className:"sticky top-6",children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx("div",{className:"text-4xl mb-2",children:n.icon}),l.jsx(Te,{className:"text-lg",children:n.title}),l.jsx(Ut,{children:n.description})]}),l.jsxs(Re,{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsxs("span",{className:"flex items-center space-x-1",children:[l.jsx(Wm,{className:"w-4 h-4"}),l.jsx("span",{children:"Est. Time"})]}),l.jsx("span",{className:"font-medium",children:n.estimated_time})]}),l.jsxs("div",{children:[l.jsx(Ue,{className:"text-sm font-medium mb-2 block",children:"Compliance Standards"}),l.jsx("div",{className:"flex flex-wrap gap-1",children:n.compliance.map(R=>l.jsx(hr,{variant:"secondary",className:"text-xs",children:R},R))})]}),l.jsxs("div",{className:"pt-4 border-t",children:[l.jsxs("div",{className:"flex items-center space-x-2 text-green-600",children:[l.jsx(Di,{className:"w-4 h-4"}),l.jsx("span",{className:"text-sm font-medium",children:"100% Free Generation"})]}),l.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"No subscription required. Uses free AI providers."})]})]})]})}),l.jsx("div",{className:"lg:col-span-2",children:l.jsxs(ke,{children:[l.jsxs(Pe,{children:[l.jsxs(Te,{children:["Generate ",n.title]}),l.jsx(Ut,{children:"Customize your SOP template with company-specific information and preferences."})]}),l.jsxs(Re,{className:"space-y-6",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Company Information"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"companyName",children:"Company Name *"}),l.jsx(Ge,{id:"companyName",value:f,onChange:R=>p(R.target.value),placeholder:"Enter your company name"})]}),l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"location",children:"Location"}),l.jsx(Ge,{id:"location",value:d,onChange:R=>x(R.target.value),placeholder:"City, State"})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Customization Options"}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:n.custom_options.map(R=>l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Iu,{id:R.id,checked:w.includes(R.id),onCheckedChange:()=>k(R.id)}),l.jsx(Ue,{htmlFor:R.id,className:"text-sm",children:R.label})]},R.id))})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"AI Provider Selection"}),l.jsx("div",{className:"space-y-2",children:S.map(R=>l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("input",{type:"radio",id:R.id,name:"llmProvider",value:R.id,checked:N===R.id,onChange:O=>b(O.target.value),className:"w-4 h-4 text-[#3498DB]"}),l.jsxs("div",{className:"flex-1",children:[l.jsx(Ue,{htmlFor:R.id,className:"font-medium",children:R.name}),l.jsx("p",{className:"text-xs text-gray-600",children:R.description})]})]},R.id))})]}),h&&l.jsxs("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[l.jsx("div",{className:"flex items-center space-x-2 text-red-700",children:l.jsx("span",{className:"font-medium",children:"Generation Failed"})}),l.jsx("p",{className:"text-sm text-red-600 mt-1",children:h})]}),o&&l.jsxs("div",{className:"space-y-4 p-4 bg-blue-50 rounded-lg",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(ar,{className:"w-5 h-5 text-[#3498DB] animate-pulse"}),l.jsx("span",{className:"font-medium",children:"Generating your SOP..."})]}),l.jsx(zu,{value:i,className:"w-full"}),l.jsx("p",{className:"text-sm text-gray-600",children:c})]}),C&&!o&&l.jsxs("div",{className:"space-y-4 p-4 bg-green-50 border border-green-200 rounded-lg",children:[l.jsxs("div",{className:"flex items-center space-x-2 text-green-700",children:[l.jsx(Di,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:"SOP Generated Successfully!"})]}),l.jsx("p",{className:"text-sm text-green-600",children:"Your SOP has been generated and is ready for download."}),l.jsx(X,{className:"w-full bg-green-600 hover:bg-green-700",children:"Download PDF"})]}),l.jsxs("div",{className:"pt-4",children:[l.jsx(X,{onClick:j,disabled:!f||o,className:"w-full bg-[#3498DB] hover:bg-[#2980B9] text-white py-3",size:"lg",children:o?l.jsxs(l.Fragment,{children:[l.jsx(ar,{className:"w-4 h-4 mr-2 animate-pulse"}),"Generating SOP Template..."]}):l.jsxs(l.Fragment,{children:[l.jsx(ar,{className:"w-4 h-4 mr-2"}),"Generate SOP Template"]})}),!f&&l.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Please enter your company name to continue."})]})]})]})})]})]})}):l.jsx(Hn,{children:l.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[l.jsx("div",{className:"flex items-center space-x-4",children:l.jsx(X,{variant:"ghost",asChild:!0,children:l.jsxs(vt,{to:"/templates",children:[l.jsx(is,{className:"w-4 h-4 mr-2"}),"Back to Templates"]})})}),l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#3498DB] mx-auto"}),l.jsx("p",{className:"mt-4 text-gray-600",children:"Loading template..."}),h&&l.jsx("p",{className:"mt-2 text-red-600",children:h})]})]})})};function aN(e,t=[]){let n=[];function r(s,i){const a=g.createContext(i),c=n.length;n=[...n,i];function u(p){const{scope:d,children:x,...w}=p,y=(d==null?void 0:d[e][c])||a,C=g.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:C,children:x})}function f(p,d){const x=(d==null?void 0:d[e][c])||a,w=g.useContext(x);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,f]}const o=()=>{const s=n.map(i=>g.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,lN(o,...t)]}function lN(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var cN=g.createContext(void 0);function uv(e){const t=g.useContext(cN);return e||t||"ltr"}var dl="rovingFocusGroup.onEntryFocus",uN={bubbles:!1,cancelable:!0},Ea="RovingFocusGroup",[wc,dv,dN]=vm(Ea),[fN,fv]=aN(Ea,[dN]),[pN,hN]=fN(Ea),pv=g.forwardRef((e,t)=>l.jsx(wc.Provider,{scope:e.__scopeRovingFocusGroup,children:l.jsx(wc.Slot,{scope:e.__scopeRovingFocusGroup,children:l.jsx(mN,{...e,ref:t})})}));pv.displayName=Ea;var mN=g.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:c,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...p}=e,d=g.useRef(null),x=at(t,d),w=uv(s),[y=null,C]=ua({prop:i,defaultProp:a,onChange:c}),[m,h]=g.useState(!1),v=Pt(u),S=dv(n),N=g.useRef(!1),[b,k]=g.useState(0);return g.useEffect(()=>{const j=d.current;if(j)return j.addEventListener(dl,v),()=>j.removeEventListener(dl,v)},[v]),l.jsx(pN,{scope:n,orientation:r,dir:w,loop:o,currentTabStopId:y,onItemFocus:g.useCallback(j=>C(j),[C]),onItemShiftTab:g.useCallback(()=>h(!0),[]),onFocusableItemAdd:g.useCallback(()=>k(j=>j+1),[]),onFocusableItemRemove:g.useCallback(()=>k(j=>j-1),[]),children:l.jsx(ce.div,{tabIndex:m||b===0?-1:0,"data-orientation":r,...p,ref:x,style:{outline:"none",...e.style},onMouseDown:ee(e.onMouseDown,()=>{N.current=!0}),onFocus:ee(e.onFocus,j=>{const R=!N.current;if(j.target===j.currentTarget&&R&&!m){const O=new CustomEvent(dl,uN);if(j.currentTarget.dispatchEvent(O),!O.defaultPrevented){const $=S().filter(B=>B.focusable),T=$.find(B=>B.active),F=$.find(B=>B.id===y),V=[T,F,...$].filter(Boolean).map(B=>B.ref.current);gv(V,f)}}N.current=!1}),onBlur:ee(e.onBlur,()=>h(!1))})})}),hv="RovingFocusGroupItem",mv=g.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...i}=e,a=sg(),c=s||a,u=hN(hv,n),f=u.currentTabStopId===c,p=dv(n),{onFocusableItemAdd:d,onFocusableItemRemove:x}=u;return g.useEffect(()=>{if(r)return d(),()=>x()},[r,d,x]),l.jsx(wc.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:l.jsx(ce.span,{tabIndex:f?0:-1,"data-orientation":u.orientation,...i,ref:t,onMouseDown:ee(e.onMouseDown,w=>{r?u.onItemFocus(c):w.preventDefault()}),onFocus:ee(e.onFocus,()=>u.onItemFocus(c)),onKeyDown:ee(e.onKeyDown,w=>{if(w.key==="Tab"&&w.shiftKey){u.onItemShiftTab();return}if(w.target!==w.currentTarget)return;const y=yN(w,u.orientation,u.dir);if(y!==void 0){if(w.metaKey||w.ctrlKey||w.altKey||w.shiftKey)return;w.preventDefault();let m=p().filter(h=>h.focusable).map(h=>h.ref.current);if(y==="last")m.reverse();else if(y==="prev"||y==="next"){y==="prev"&&m.reverse();const h=m.indexOf(w.currentTarget);m=u.loop?xN(m,h+1):m.slice(h+1)}setTimeout(()=>gv(m))}})})})});mv.displayName=hv;var gN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function vN(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function yN(e,t,n){const r=vN(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return gN[r]}function gv(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function xN(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var wN=pv,CN=mv,Bu="Tabs",[SN,UN]=ca(Bu,[fv]),vv=fv(),[NN,Uu]=SN(Bu),yv=g.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:s,orientation:i="horizontal",dir:a,activationMode:c="automatic",...u}=e,f=uv(a),[p,d]=ua({prop:r,onChange:o,defaultProp:s});return l.jsx(NN,{scope:n,baseId:sg(),value:p,onValueChange:d,orientation:i,dir:f,activationMode:c,children:l.jsx(ce.div,{dir:f,"data-orientation":i,...u,ref:t})})});yv.displayName=Bu;var xv="TabsList",wv=g.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,s=Uu(xv,n),i=vv(n);return l.jsx(wN,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:r,children:l.jsx(ce.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});wv.displayName=xv;var Cv="TabsTrigger",Sv=g.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...s}=e,i=Uu(Cv,n),a=vv(n),c=bv(i.baseId,r),u=jv(i.baseId,r),f=r===i.value;return l.jsx(CN,{asChild:!0,...a,focusable:!o,active:f,children:l.jsx(ce.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...s,ref:t,onMouseDown:ee(e.onMouseDown,p=>{!o&&p.button===0&&p.ctrlKey===!1?i.onValueChange(r):p.preventDefault()}),onKeyDown:ee(e.onKeyDown,p=>{[" ","Enter"].includes(p.key)&&i.onValueChange(r)}),onFocus:ee(e.onFocus,()=>{const p=i.activationMode!=="manual";!f&&!o&&p&&i.onValueChange(r)})})})});Sv.displayName=Cv;var Nv="TabsContent",Ev=g.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:s,...i}=e,a=Uu(Nv,n),c=bv(a.baseId,r),u=jv(a.baseId,r),f=r===a.value,p=g.useRef(f);return g.useEffect(()=>{const d=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(d)},[]),l.jsx(Ss,{present:o||f,children:({present:d})=>l.jsx(ce.div,{"data-state":f?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":c,hidden:!d,id:u,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:d&&s})})});Ev.displayName=Nv;function bv(e,t){return`${e}-trigger-${t}`}function jv(e,t){return`${e}-content-${t}`}var EN=yv,kv=wv,Pv=Sv,Tv=Ev;const bN=EN,Rv=g.forwardRef(({className:e,...t},n)=>l.jsx(kv,{ref:n,className:me("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Rv.displayName=kv.displayName;const Cc=g.forwardRef(({className:e,...t},n)=>l.jsx(Pv,{ref:n,className:me("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));Cc.displayName=Pv.displayName;const Sc=g.forwardRef(({className:e,...t},n)=>l.jsx(Tv,{ref:n,className:me("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Sc.displayName=Tv.displayName;const jN=({templateData:e,brandConfig:t,onPreviewReady:n,className:r=""})=>{const{api:o}=$u(),[s,i]=g.useState(),[a,c]=g.useState(!1),[u,f]=g.useState(),[p,d]=g.useState(),x=g.useCallback(async()=>{if(!e||a)return;const C=JSON.stringify({templateData:e,brandConfig:t});if(C!==p){c(!0),f(void 0);try{const h=`data:application/pdf;base64,${(await o.generatePreview(e,t)).preview_base64}`;i(h),d(C),n&&n(h)}catch(m){console.error("Preview generation failed:",m),f(m instanceof Error?m.message:"Failed to generate preview")}finally{c(!1)}}},[e,t,o,a,p,n]);g.useEffect(()=>{const C=setTimeout(x,1e3);return()=>clearTimeout(C)},[x]);const w=()=>{d(""),x()},y=()=>{if(s){const C=document.createElement("a");C.href=s,C.download=`${(e==null?void 0:e.title)||"SOP"}_Preview.pdf`,document.body.appendChild(C),C.click(),document.body.removeChild(C)}};return l.jsxs(ke,{className:`pdf-preview-container ${r}`,children:[l.jsx(Pe,{className:"pb-3",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs(Te,{className:"text-lg flex items-center space-x-2",children:[l.jsx(Fi,{className:"w-5 h-5"}),l.jsx("span",{children:"PDF Preview"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[a&&l.jsxs("div",{className:"flex items-center space-x-2 text-blue-600",children:[l.jsx(sl,{className:"w-4 h-4 animate-spin"}),l.jsx("span",{className:"text-sm",children:"Generating..."})]}),l.jsxs(X,{variant:"outline",size:"sm",onClick:w,disabled:a,className:"flex items-center space-x-1",children:[l.jsx(v1,{className:`w-4 h-4 ${a?"animate-spin":""}`}),l.jsx("span",{children:"Refresh"})]}),s&&l.jsxs(X,{variant:"default",size:"sm",onClick:y,className:"flex items-center space-x-1",children:[l.jsx(ma,{className:"w-4 h-4"}),l.jsx("span",{children:"Download"})]})]})]})}),l.jsx(Re,{className:"p-0",children:l.jsx("div",{className:"preview-content border-t",children:u?l.jsxs("div",{className:"flex flex-col items-center justify-center h-96 p-6 text-center",children:[l.jsx(h1,{className:"w-12 h-12 text-red-500 mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-red-700 mb-2",children:"Preview Error"}),l.jsx("p",{className:"text-red-600 mb-4",children:u}),l.jsx(X,{onClick:w,variant:"outline",children:"Try Again"})]}):s?l.jsxs("div",{className:"relative",children:[l.jsx("iframe",{src:s,className:"w-full h-96 border-0",title:"PDF Preview",loading:"lazy",style:{minHeight:"600px"}}),a&&l.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center",children:l.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[l.jsx(sl,{className:"w-8 h-8 animate-spin text-blue-600"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Updating preview..."})]})})]}):l.jsxs("div",{className:"flex flex-col items-center justify-center h-96 p-6 text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-4",children:l.jsx(Fi,{className:"w-8 h-8 text-gray-400"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"PDF Preview"}),l.jsx("p",{className:"text-gray-500 mb-4",children:a?"Generating your PDF preview...":"Preview will appear here when template data is available"}),a&&l.jsx(sl,{className:"w-6 h-6 animate-spin text-blue-600"})]})})})]})},kN=({brandConfig:e,onBrandConfigChange:t,onLogoUpload:n,className:r=""})=>{const[o,s]=g.useState(e),[i,a]=g.useState(null),[c,u]=g.useState(null),[f,p]=g.useState(!1);g.useEffect(()=>{s(e)},[e]);const d=(m,h)=>{const v={...o,[m]:h};s(v),t(v)},x=(m,h)=>{const v={...o,[m]:h};s(v),t(v)},w=m=>{var v;const h=(v=m.target.files)==null?void 0:v[0];if(h){if(!h.type.startsWith("image/")){alert("Please select an image file");return}if(h.size>5*1024*1024){alert("File size must be less than 5MB");return}a(h);const S=new FileReader;S.onload=N=>{var b;u((b=N.target)==null?void 0:b.result)},S.readAsDataURL(h)}},y=async()=>{if(!(!i||!n)){p(!0);try{const m=await n(i),h={...o,logo_url:m};s(h),t(h),a(null),u(null)}catch(m){console.error("Logo upload failed:",m),alert("Failed to upload logo. Please try again.")}finally{p(!1)}}},C=()=>{const m={primary_color:"#2C3E50",secondary_color:"#3498DB",company_name:"",tagline:""};s(m),t(m),a(null),u(null)};return l.jsxs(ke,{className:`brand-customizer ${r}`,children:[l.jsx(Pe,{children:l.jsxs(Te,{className:"flex items-center space-x-2",children:[l.jsx(pf,{className:"w-5 h-5"}),l.jsx("span",{children:"Brand Customization"})]})}),l.jsxs(Re,{className:"space-y-6",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[l.jsx(u1,{className:"w-4 h-4"}),l.jsx("h3",{className:"font-medium",children:"Company Information"})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"company-name",children:"Company Name"}),l.jsx(Ge,{id:"company-name",value:o.company_name||"",onChange:m=>x("company_name",m.target.value),placeholder:"Your Company Name"})]}),l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"tagline",children:"Tagline"}),l.jsx(Ge,{id:"tagline",value:o.tagline||"",onChange:m=>x("tagline",m.target.value),placeholder:"Your company tagline"})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[l.jsx(ff,{className:"w-4 h-4"}),l.jsx("h3",{className:"font-medium",children:"Company Logo"})]}),l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("div",{className:"w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50",children:c||o.logo_url?l.jsx("img",{src:c||o.logo_url,alt:"Logo preview",className:"w-full h-full object-contain rounded-lg"}):l.jsx(ff,{className:"w-8 h-8 text-gray-400"})}),l.jsxs("div",{className:"flex-1 space-y-2",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Ge,{type:"file",accept:"image/*",onChange:w,className:"flex-1"}),i&&l.jsxs(X,{onClick:y,disabled:f,size:"sm",children:[l.jsx(C1,{className:"w-4 h-4 mr-1"}),f?"Uploading...":"Upload"]})]}),l.jsx("p",{className:"text-xs text-gray-500",children:"PNG, JPG, or SVG. Max 5MB. Recommended: 200x80px"})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[l.jsx(pf,{className:"w-4 h-4"}),l.jsx("h3",{className:"font-medium",children:"Color Scheme"})]}),l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"primary-color",children:"Primary Color"}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Ge,{id:"primary-color",type:"color",value:o.primary_color||"#2C3E50",onChange:m=>d("primary_color",m.target.value),className:"w-12 h-10 p-1 border rounded"}),l.jsx(Ge,{value:o.primary_color||"#2C3E50",onChange:m=>d("primary_color",m.target.value),placeholder:"#2C3E50",className:"flex-1"})]})]}),l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"secondary-color",children:"Secondary Color"}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Ge,{id:"secondary-color",type:"color",value:o.secondary_color||"#3498DB",onChange:m=>d("secondary_color",m.target.value),className:"w-12 h-10 p-1 border rounded"}),l.jsx(Ge,{value:o.secondary_color||"#3498DB",onChange:m=>d("secondary_color",m.target.value),placeholder:"#3498DB",className:"flex-1"})]})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[l.jsx(Fi,{className:"w-4 h-4"}),l.jsx("h3",{className:"font-medium",children:"Brand Preview"})]}),l.jsx("div",{className:"p-4 rounded-lg border",style:{backgroundColor:o.primary_color||"#2C3E50",color:"#FFFFFF"},children:l.jsxs("div",{className:"flex items-center space-x-3",children:[(c||o.logo_url)&&l.jsx("img",{src:c||o.logo_url,alt:"Logo",className:"w-12 h-12 object-contain bg-white rounded p-1"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-bold text-lg",children:o.company_name||"Your Company Name"}),o.tagline&&l.jsx("p",{className:"text-sm opacity-90",children:o.tagline})]})]})})]}),l.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[l.jsxs(X,{variant:"outline",onClick:C,className:"flex items-center space-x-1",children:[l.jsx(Gm,{className:"w-4 h-4"}),l.jsx("span",{children:"Reset to Defaults"})]}),l.jsx("div",{className:"text-sm text-gray-500",children:"Changes are applied automatically"})]})]})]})},PN=()=>{const{templateId:e}=Mu(),{api:t}=$u(),[n,r]=g.useState(null),[o,s]=g.useState(!1),[i,a]=g.useState(0),[c,u]=g.useState(""),[f,p]=g.useState(""),[d,x]=g.useState(""),[w,y]=g.useState([]),[C,m]=g.useState(null),[h,v]=g.useState(null),[S,N]=g.useState({primary_color:"#2C3E50",secondary_color:"#3498DB",company_name:"",tagline:""});g.useEffect(()=>{(async()=>{if(e)try{const F=await t.getTemplateById(e);r(F);const L=F.custom_options.filter(V=>V.default).map(V=>V.id);y(L)}catch(F){console.error("Failed to load template:",F),v("Failed to load template. Please try again.")}})()},[e,t]),g.useEffect(()=>{N(T=>({...T,company_name:f}))},[f]);const b=[{id:"automatic",name:"Automatic (Recommended)",description:"Tries OpenRouter → Groq → Hugging Face → Together AI"},{id:"openrouter",name:"OpenRouter",description:"deepseek-chat (free tier)"},{id:"groq",name:"Groq",description:"llama-3.1-70b-versatile (fastest, free tier)"},{id:"huggingface",name:"Hugging Face",description:"300 requests/hour free"},{id:"together",name:"Together AI",description:"$25 free credits"}],[k,j]=g.useState("automatic"),R=T=>{y(F=>F.includes(T)?F.filter(L=>L!==T):[...F,T])},O=async()=>{if(!(!n||!f.trim())){s(!0),a(0),u("Starting generation..."),v(null);try{const T=await t.startGeneration({template_id:n.id,company_info:{name:f.trim(),location:d.trim()||void 0},customization:{selected_options:w,brand_config:S},llm_provider:k});await t.pollGenerationStatus(T.generation_id,F=>{a(F.progress),u(F.current_step||"Processing..."),F.status==="completed"&&F.result&&(m(F.result),s(!1))})}catch(T){console.error("Generation failed:",T),v(T instanceof Error?T.message:"Generation failed. Please try again."),s(!1),u("")}}},$=async T=>new Promise(F=>{const L=new FileReader;L.onload=()=>{F(L.result)},L.readAsDataURL(T)});return n?l.jsx(Hn,{children:l.jsxs("div",{className:"max-w-7xl mx-auto space-y-6",children:[l.jsx("div",{className:"flex items-center space-x-4",children:l.jsx(X,{variant:"ghost",asChild:!0,children:l.jsxs(vt,{to:"/templates",children:[l.jsx(is,{className:"w-4 h-4 mr-2"}),"Back to Templates"]})})}),l.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-3 gap-6",children:[l.jsx("div",{className:"xl:col-span-1",children:l.jsxs(ke,{className:"sticky top-6",children:[l.jsxs(Pe,{className:"text-center",children:[l.jsx("div",{className:"text-4xl mb-2",children:n.icon}),l.jsx(Te,{className:"text-lg",children:n.title}),l.jsx(Ut,{children:n.description})]}),l.jsxs(Re,{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsxs("span",{className:"flex items-center space-x-1",children:[l.jsx(Wm,{className:"w-4 h-4"}),l.jsx("span",{children:"Est. Time"})]}),l.jsx("span",{className:"font-medium",children:n.estimated_time})]}),l.jsxs("div",{children:[l.jsx(Ue,{className:"text-sm font-medium mb-2 block",children:"Compliance Standards"}),l.jsx("div",{className:"flex flex-wrap gap-1",children:n.compliance.map(T=>l.jsx(hr,{variant:"secondary",className:"text-xs",children:T},T))})]}),l.jsxs("div",{className:"pt-4 border-t",children:[l.jsxs("div",{className:"flex items-center space-x-2 text-green-600",children:[l.jsx(Di,{className:"w-4 h-4"}),l.jsx("span",{className:"text-sm font-medium",children:"100% Free Generation"})]}),l.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"No subscription required. Uses free AI providers."})]})]})]})}),l.jsx("div",{className:"xl:col-span-1",children:l.jsxs(bN,{defaultValue:"generate",className:"space-y-4",children:[l.jsxs(Rv,{className:"grid w-full grid-cols-2",children:[l.jsx(Cc,{value:"generate",children:"Generate"}),l.jsxs(Cc,{value:"customize",children:[l.jsx(Km,{className:"w-4 h-4 mr-1"}),"Customize"]})]}),l.jsx(Sc,{value:"generate",className:"space-y-4",children:l.jsxs(ke,{children:[l.jsxs(Pe,{children:[l.jsxs(Te,{children:["Generate ",n.title]}),l.jsx(Ut,{children:"Customize your SOP template with company-specific information."})]}),l.jsxs(Re,{className:"space-y-6",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Company Information"}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"companyName",children:"Company Name *"}),l.jsx(Ge,{id:"companyName",value:f,onChange:T=>p(T.target.value),placeholder:"Enter your company name"})]}),l.jsxs("div",{children:[l.jsx(Ue,{htmlFor:"location",children:"Location"}),l.jsx(Ge,{id:"location",value:d,onChange:T=>x(T.target.value),placeholder:"City, State"})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"Customization Options"}),l.jsx("div",{className:"space-y-3",children:n.custom_options.map(T=>l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Iu,{id:T.id,checked:w.includes(T.id),onCheckedChange:()=>R(T.id)}),l.jsx(Ue,{htmlFor:T.id,className:"text-sm",children:T.label})]},T.id))})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-medium",children:"AI Provider Selection"}),l.jsx("div",{className:"space-y-2",children:b.map(T=>l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("input",{type:"radio",id:T.id,name:"llmProvider",value:T.id,checked:k===T.id,onChange:F=>j(F.target.value),className:"w-4 h-4 text-[#3498DB]"}),l.jsxs("div",{className:"flex-1",children:[l.jsx(Ue,{htmlFor:T.id,className:"font-medium",children:T.name}),l.jsx("p",{className:"text-xs text-gray-600",children:T.description})]})]},T.id))})]}),h&&l.jsxs("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[l.jsx("div",{className:"flex items-center space-x-2 text-red-700",children:l.jsx("span",{className:"font-medium",children:"Generation Failed"})}),l.jsx("p",{className:"text-sm text-red-600 mt-1",children:h})]}),o&&l.jsxs("div",{className:"space-y-4 p-4 bg-blue-50 rounded-lg",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(ar,{className:"w-5 h-5 text-[#3498DB] animate-pulse"}),l.jsx("span",{className:"font-medium",children:"Generating your SOP..."})]}),l.jsx(zu,{value:i,className:"w-full"}),l.jsx("p",{className:"text-sm text-gray-600",children:c})]}),C&&!o&&l.jsxs("div",{className:"space-y-4 p-4 bg-green-50 border border-green-200 rounded-lg",children:[l.jsxs("div",{className:"flex items-center space-x-2 text-green-700",children:[l.jsx(Di,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:"SOP Generated Successfully!"})]}),l.jsx("p",{className:"text-sm text-green-600",children:"Your SOP has been generated and is ready for download."})]}),l.jsxs("div",{className:"pt-4",children:[l.jsx(X,{onClick:O,disabled:!f||o,className:"w-full bg-[#3498DB] hover:bg-[#2980B9] text-white py-3",size:"lg",children:o?l.jsxs(l.Fragment,{children:[l.jsx(ar,{className:"w-4 h-4 mr-2 animate-pulse"}),"Generating SOP Template..."]}):l.jsxs(l.Fragment,{children:[l.jsx(ar,{className:"w-4 h-4 mr-2"}),"Generate SOP Template"]})}),!f&&l.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Please enter your company name to continue."})]})]})]})}),l.jsx(Sc,{value:"customize",children:l.jsx(kN,{brandConfig:S,onBrandConfigChange:N,onLogoUpload:$})})]})}),l.jsx("div",{className:"xl:col-span-1",children:l.jsx("div",{className:"sticky top-6",children:l.jsx(jN,{templateData:C==null?void 0:C.template_data,brandConfig:S})})})]})]})}):l.jsx(Hn,{children:l.jsxs("div",{className:"max-w-7xl mx-auto space-y-6",children:[l.jsx("div",{className:"flex items-center space-x-4",children:l.jsx(X,{variant:"ghost",asChild:!0,children:l.jsxs(vt,{to:"/templates",children:[l.jsx(is,{className:"w-4 h-4 mr-2"}),"Back to Templates"]})})}),l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#3498DB] mx-auto"}),l.jsx("p",{className:"mt-4 text-gray-600",children:"Loading template..."}),h&&l.jsx("p",{className:"mt-2 text-red-600",children:h})]})]})})},TN=()=>{const[e,t]=g.useState(""),n=[{id:"doc-001",title:"Restaurant Opening Procedures - Bella Vista",templateType:"Restaurant Operations",createdDate:"2025-01-29",provider:"Groq",model:"llama-3.1-70b-versatile",fileSize:"2.3 MB",status:"completed",downloadCount:5,icon:"🍽️"},{id:"doc-002",title:"Healthcare Patient Intake - MedCenter Pro",templateType:"Healthcare Procedures",createdDate:"2025-01-28",provider:"Hugging Face",model:"Open Source Model",fileSize:"3.1 MB",status:"completed",downloadCount:2,icon:"🏥"},{id:"doc-003",title:"IT Employee Onboarding - TechCorp Solutions",templateType:"IT Onboarding",createdDate:"2025-01-27",provider:"Together AI",model:"Free Tier",fileSize:"2.8 MB",status:"completed",downloadCount:8,icon:"💻"},{id:"doc-004",title:"Customer Service Escalation - ServiceFirst Inc",templateType:"Customer Service",createdDate:"2025-01-26",provider:"OpenRouter",model:"deepseek-chat",fileSize:"1.9 MB",status:"completed",downloadCount:3,icon:"📞"},{id:"doc-005",title:"Restaurant Cleaning Procedures - Quick Bites",templateType:"Restaurant Operations",createdDate:"2025-01-25",provider:"Groq",model:"llama-3.1-70b-versatile",fileSize:"2.7 MB",status:"completed",downloadCount:12,icon:"🧽"}],r=n.filter(s=>s.title.toLowerCase().includes(e.toLowerCase())||s.templateType.toLowerCase().includes(e.toLowerCase())),o=s=>({Groq:"bg-purple-100 text-purple-800","Hugging Face":"bg-yellow-100 text-yellow-800","Together AI":"bg-green-100 text-green-800",OpenRouter:"bg-blue-100 text-blue-800"})[s]||"bg-gray-100 text-gray-800";return l.jsx(Hn,{children:l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex justify-between items-start",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-[#2C3E50] mb-2",children:"Generated Documents"}),l.jsx("p",{className:"text-gray-600",children:"View and manage your generated SOP documents."})]}),l.jsx(X,{asChild:!0,className:"bg-[#3498DB] hover:bg-[#2980B9]",children:l.jsx(vt,{to:"/templates",children:"Create New SOP"})})]}),l.jsxs("div",{className:"relative max-w-md",children:[l.jsx(Qm,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx(Ge,{placeholder:"Search documents...",value:e,onChange:s=>t(s.target.value),className:"pl-10"})]}),l.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:r.map(s=>l.jsxs(ke,{className:"hover:shadow-lg transition-shadow",children:[l.jsxs(Pe,{children:[l.jsxs("div",{className:"flex items-start justify-between",children:[l.jsx("div",{className:"text-2xl mb-2",children:s.icon}),l.jsx(hr,{className:o(s.provider),children:s.provider})]}),l.jsx(Te,{className:"text-lg leading-tight line-clamp-2",children:s.title}),l.jsx(Ut,{className:"text-sm",children:s.templateType})]}),l.jsxs(Re,{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(d1,{className:"w-4 h-4"}),l.jsx("span",{children:new Date(s.createdDate).toLocaleDateString()})]}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(as,{className:"w-4 h-4"}),l.jsx("span",{children:s.fileSize})]})]}),l.jsxs("div",{className:"text-xs text-gray-500",children:["Generated with ",s.model]}),l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsxs("span",{className:"text-gray-600",children:["Downloaded ",s.downloadCount," time",s.downloadCount!==1?"s":""]}),l.jsx(hr,{variant:"outline",className:"text-xs",children:s.status})]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsxs(X,{variant:"outline",size:"sm",className:"flex-1",children:[l.jsx(Fi,{className:"w-4 h-4 mr-1"}),"Preview"]}),l.jsxs(X,{size:"sm",className:"flex-1 bg-[#3498DB] hover:bg-[#2980B9]",children:[l.jsx(ma,{className:"w-4 h-4 mr-1"}),"Download"]}),l.jsx(X,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:l.jsx(w1,{className:"w-4 h-4"})})]})]})]},s.id))}),r.length===0&&l.jsxs("div",{className:"text-center py-12",children:[l.jsx(as,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No documents found"}),l.jsx("p",{className:"text-gray-600 mb-4",children:e?"Try adjusting your search criteria.":"You haven't generated any SOPs yet."}),l.jsx(X,{asChild:!0,className:"bg-[#3498DB] hover:bg-[#2980B9]",children:l.jsx(vt,{to:"/templates",children:"Generate Your First SOP"})})]}),r.length>0&&l.jsxs("div",{className:"text-center text-gray-600",children:["Showing ",r.length," of ",n.length," documents"]})]})})},RN=()=>{const{documentId:e}=Mu(),[t,n]=g.useState(100),r={id:e,title:"Restaurant Opening Procedures - Bella Vista",templateType:"Restaurant Operations",createdDate:"2025-01-29",provider:"Groq",model:"llama-3.1-70b-versatile",fileSize:"2.3 MB",pages:12,sections:["Introduction & Purpose","Daily Opening Checklist","Equipment Safety Checks","Food Safety Protocols","HACCP Procedures","Staff Preparation","Cleaning & Sanitization","Inventory Management","Emergency Procedures","Compliance Documentation","Quality Control","Appendices"],generationStats:{totalSections:6,successfulSections:6,generationTimeSeconds:23.45,providerUsed:"groq",modelUsed:"llama-3.1-70b-versatile"},compliance:["FDA Food Code","HACCP","ServSafe"],icon:"🍽️"},o=()=>{n(c=>Math.min(c+25,200))},s=()=>{n(c=>Math.max(c-25,50))},i=()=>{n(100)},a=c=>({Groq:"bg-purple-100 text-purple-800","Hugging Face":"bg-yellow-100 text-yellow-800","Together AI":"bg-green-100 text-green-800",OpenRouter:"bg-blue-100 text-blue-800"})[c]||"bg-gray-100 text-gray-800";return l.jsx(Hn,{children:l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(X,{variant:"ghost",asChild:!0,children:l.jsxs(vt,{to:"/documents",children:[l.jsx(is,{className:"w-4 h-4 mr-2"}),"Back to Documents"]})}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-[#2C3E50]",children:r.title}),l.jsx("p",{className:"text-gray-600",children:r.templateType})]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs(X,{variant:"outline",children:[l.jsx(y1,{className:"w-4 h-4 mr-2"}),"Share"]}),l.jsxs(X,{className:"bg-[#3498DB] hover:bg-[#2980B9]",children:[l.jsx(ma,{className:"w-4 h-4 mr-2"}),"Download PDF"]})]})]}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[l.jsx("div",{className:"lg:col-span-1",children:l.jsxs(ke,{className:"sticky top-6",children:[l.jsxs(Pe,{children:[l.jsx("div",{className:"text-3xl mb-2",children:r.icon}),l.jsx(Te,{className:"text-lg",children:"Document Details"})]}),l.jsxs(Re,{className:"space-y-4",children:[l.jsxs("div",{className:"space-y-2",children:[l.jsxs("div",{className:"flex justify-between text-sm",children:[l.jsx("span",{className:"text-gray-600",children:"Created:"}),l.jsx("span",{className:"font-medium",children:new Date(r.createdDate).toLocaleDateString()})]}),l.jsxs("div",{className:"flex justify-between text-sm",children:[l.jsx("span",{className:"text-gray-600",children:"Pages:"}),l.jsx("span",{className:"font-medium",children:r.pages})]}),l.jsxs("div",{className:"flex justify-between text-sm",children:[l.jsx("span",{className:"text-gray-600",children:"File Size:"}),l.jsx("span",{className:"font-medium",children:r.fileSize})]}),l.jsxs("div",{className:"flex justify-between text-sm",children:[l.jsx("span",{className:"text-gray-600",children:"Provider:"}),l.jsx(hr,{className:a(r.provider),children:r.provider})]})]}),l.jsxs("div",{className:"pt-4 border-t",children:[l.jsx("h4",{className:"font-medium mb-2",children:"Compliance Standards"}),l.jsx("div",{className:"flex flex-wrap gap-1",children:r.compliance.map(c=>l.jsx(hr,{variant:"secondary",className:"text-xs",children:c},c))})]}),l.jsxs("div",{className:"pt-4 border-t",children:[l.jsx("h4",{className:"font-medium mb-2",children:"Table of Contents"}),l.jsx("div",{className:"space-y-1 max-h-64 overflow-y-auto",children:r.sections.map((c,u)=>l.jsxs("div",{className:"text-sm text-gray-600 hover:text-[#3498DB] cursor-pointer py-1",children:[u+1,". ",c]},u))})]}),l.jsxs("div",{className:"pt-4 border-t",children:[l.jsx("h4",{className:"font-medium mb-2",children:"Generation Stats"}),l.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[l.jsxs("div",{children:["Sections: ",r.generationStats.successfulSections,"/",r.generationStats.totalSections]}),l.jsxs("div",{children:["Time: ",r.generationStats.generationTimeSeconds,"s"]}),l.jsxs("div",{children:["Model: ",r.generationStats.modelUsed]})]})]})]})]})}),l.jsx("div",{className:"lg:col-span-3",children:l.jsxs(ke,{children:[l.jsx(Pe,{children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx(Te,{children:"Document Preview"}),l.jsx(Ut,{children:"Interactive PDF preview with navigation controls"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(X,{variant:"outline",size:"sm",onClick:s,children:l.jsx(j1,{className:"w-4 h-4"})}),l.jsx(X,{variant:"outline",size:"sm",onClick:i,children:l.jsx(Gm,{className:"w-4 h-4"})}),l.jsx(X,{variant:"outline",size:"sm",onClick:o,children:l.jsx(b1,{className:"w-4 h-4"})}),l.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:[t,"%"]})]})]})}),l.jsx(Re,{children:l.jsx("div",{className:"border rounded-lg bg-white shadow-inner min-h-[600px] flex items-center justify-center",style:{transform:`scale(${t/100})`,transformOrigin:"top left"},children:l.jsxs("div",{className:"bg-white shadow-lg max-w-2xl w-full min-h-[800px] p-8 border",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx("h1",{className:"text-3xl font-bold text-[#2C3E50] mb-2",children:"Restaurant Opening Procedures"}),l.jsx("h2",{className:"text-xl text-gray-600 mb-4",children:"Bella Vista Restaurant"}),l.jsx("div",{className:"w-16 h-16 bg-[#2C3E50] rounded-lg mx-auto mb-4 flex items-center justify-center text-2xl",children:"🍽️"}),l.jsx("p",{className:"text-gray-500",children:"Standard Operating Procedures"}),l.jsxs("p",{className:"text-sm text-gray-400 mt-2",children:["Generated on ",new Date(r.createdDate).toLocaleDateString()]})]}),l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-bold text-[#2C3E50] mb-3 border-b pb-2",children:"1. Introduction & Purpose"}),l.jsx("p",{className:"text-gray-700 leading-relaxed",children:"This Standard Operating Procedure (SOP) provides comprehensive guidelines for the daily opening procedures at Bella Vista Restaurant. These procedures ensure compliance with FDA Food Code, HACCP requirements, and ServSafe standards while maintaining the highest levels of food safety and operational efficiency."})]}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-bold text-[#2C3E50] mb-3 border-b pb-2",children:"2. Daily Opening Checklist"}),l.jsxs("div",{className:"space-y-2",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border border-gray-400 rounded"}),l.jsx("span",{className:"text-gray-700",children:"Verify all refrigeration units are at proper temperatures (≤41°F)"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border border-gray-400 rounded"}),l.jsx("span",{className:"text-gray-700",children:"Check hot holding equipment reaches 165°F minimum"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border border-gray-400 rounded"}),l.jsx("span",{className:"text-gray-700",children:"Inspect all food items for signs of spoilage or contamination"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-4 h-4 border border-gray-400 rounded"}),l.jsx("span",{className:"text-gray-700",children:"Verify proper hand washing station setup and supplies"})]})]})]}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-bold text-[#2C3E50] mb-3 border-b pb-2",children:"3. Equipment Safety Checks"}),l.jsx("p",{className:"text-gray-700 leading-relaxed",children:"All kitchen equipment must be inspected for proper operation and safety compliance before beginning food preparation activities..."})]})]}),l.jsx("div",{className:"mt-8 pt-4 border-t text-center",children:l.jsxs("p",{className:"text-xs text-gray-400",children:["Page 1 of ",r.pages," | Generated by Premium SOP Templates"]})})]})})})]})})]})]})})},_N=()=>{const e=go();return g.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:l.jsxs("div",{className:"text-center",children:[l.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),l.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),l.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},ON=new MC,AN=()=>l.jsx(IC,{client:ON,children:l.jsxs(dC,{children:[l.jsx(lw,{}),l.jsx(Dw,{}),l.jsx(OS,{children:l.jsxs(bS,{children:[l.jsx(hn,{path:"/",element:l.jsx(FS,{})}),l.jsx(hn,{path:"/templates",element:l.jsx($S,{})}),l.jsx(hn,{path:"/generate/:templateId",element:l.jsx(iN,{})}),l.jsx(hn,{path:"/generate-advanced/:templateId",element:l.jsx(PN,{})}),l.jsx(hn,{path:"/documents",element:l.jsx(TN,{})}),l.jsx(hn,{path:"/preview/:documentId",element:l.jsx(RN,{})}),l.jsx(hn,{path:"*",element:l.jsx(_N,{})})]})})]})});hm(document.getElementById("root")).render(l.jsx(AN,{}));
