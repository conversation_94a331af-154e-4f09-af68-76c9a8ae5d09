standards:
  - ISO 27001 Information Security
  - NIST Cybersecurity Framework
  - SOX IT Controls
  - GDPR Data Protection
  - Company IT Security Policies

regulatory_links:
  ISO_27001: "https://www.iso.org/isoiec-27001-information-security.html"
  NIST: "https://www.nist.gov/cyberframework"
  SOX: "https://www.sec.gov/about/laws/soa2002.pdf"
  GDPR: "https://gdpr.eu/"
  SANS: "https://www.sans.org/security-awareness-training/"

sections:
  - name: Introduction
    order: 1
    required: true
    has_checklist: false
    requirements:
      compliance:
        - "IT security policy overview"
        - "Data protection requirements"
        - "Access control principles"
    
  - name: Account Setup and Access
    order: 2
    required: true
    has_checklist: true
    checklist_items:
      - "Create user account with appropriate permissions"
      - "Set up multi-factor authentication"
      - "Assign security groups based on role"
      - "Configure email and calendar access"
      - "Provide VPN access if required"
      - "Document all access granted"
    requirements:
      compliance:
        - "Principle of least privilege"
        - "Role-based access control"
        - "Identity verification procedures"
        
  - name: Security Training and Awareness
    order: 3
    required: true
    has_checklist: true
    checklist_items:
      - "Complete mandatory security awareness training"
      - "Review and acknowledge IT security policies"
      - "Complete phishing simulation training"
      - "Understand incident reporting procedures"
      - "Learn password management best practices"
      - "Review data classification guidelines"
    requirements:
      compliance:
        - "Security awareness training requirements"
        - "Policy acknowledgment documentation"
        - "Ongoing security education"
        
  - name: Equipment and Software Setup
    order: 4
    required: true
    has_checklist: true
    checklist_items:
      - "Install and configure endpoint protection"
      - "Set up automatic software updates"
      - "Configure backup and recovery solutions"
      - "Install approved business applications"
      - "Configure email encryption"
      - "Test remote access capabilities"
    requirements:
      compliance:
        - "Endpoint security standards"
        - "Software licensing compliance"
        - "Data backup requirements"
        
  - name: Data Protection and Privacy
    order: 5
    required: true
    has_checklist: true
    checklist_items:
      - "Review data classification and handling procedures"
      - "Understand data retention policies"
      - "Learn secure data transmission methods"
      - "Configure file sharing permissions"
      - "Set up secure communication channels"
      - "Review privacy impact assessments"
    requirements:
      compliance:
        - "GDPR data protection requirements"
        - "Data classification standards"
        - "Privacy by design principles"
        
  - name: Incident Response and Reporting
    order: 6
    required: true
    has_checklist: false
    emergency_contacts:
      - "IT Security Team"
      - "Help Desk"
      - "Data Protection Officer"
      - "Incident Response Team"
    requirements:
      compliance:
        - "Incident reporting procedures"
        - "Security event escalation"
        - "Business continuity planning"

security_requirements:
  password_policy:
    minimum_length: 12
    complexity: "Upper, lower, number, special character"
    expiration_days: 90
    history_count: 12
    
  access_controls:
    - "Multi-factor authentication required"
    - "Regular access reviews"
    - "Automated account lockout"
    - "Session timeout controls"
    
  data_handling:
    - "Encrypt data at rest and in transit"
    - "Classify data according to sensitivity"
    - "Implement data loss prevention"
    - "Regular data backup and testing"

training_modules:
  mandatory:
    - "Information Security Fundamentals"
    - "Phishing and Social Engineering"
    - "Data Protection and Privacy"
    - "Incident Response Procedures"
    - "Acceptable Use Policy"
    
  role_specific:
    - "Developer Security Training"
    - "Administrator Privileged Access"
    - "Manager Security Responsibilities"
    - "Remote Work Security"

critical_violations:
  - "Unauthorized access attempts"
  - "Data breaches or leaks"
  - "Malware infections"
  - "Policy violations"
  - "Unencrypted sensitive data"
  - "Weak or shared passwords"

compliance_checkpoints:
  day_1:
    - "Account creation and initial access"
    - "Security policy acknowledgment"
    - "Equipment assignment and setup"
    
  week_1:
    - "Complete security training modules"
    - "Verify system access and functionality"
    - "Review role-specific security requirements"
    
  month_1:
    - "Security awareness assessment"
    - "Access review and validation"
    - "Feedback on onboarding process"
