/**
 * API Service for SOP Builder MVP
 * Connects React frontend to Python backend
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Types
export interface Template {
  id: string;
  title: string;
  description: string;
  industry: string;
  icon: string;
  estimated_time: string;
  compliance: string[];
  custom_options: TemplateOption[];
}

export interface TemplateOption {
  id: string;
  label: string;
  default: boolean;
  required?: boolean;
}

export interface GenerationRequest {
  template_id: string;
  company_info: {
    name: string;
    location?: string;
  };
  customization?: {
    selected_options: string[];
    brand_config?: BrandConfig;
  };
  llm_provider?: string;
}

export interface GenerationResponse {
  generation_id: string;
  status: string;
  estimated_completion?: string;
  websocket_url?: string;
}

export interface GenerationStatus {
  generation_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  current_step?: string;
  result?: any;
  error?: string;
}

export interface BrandConfig {
  primary_color?: string;
  secondary_color?: string;
  company_name?: string;
  tagline?: string;
  logo_url?: string;
}

export interface Industry {
  id: string;
  name: string;
  template_count: number;
  compliance_standards: string[];
}

// API Client Class
class SOPBuilderAPI {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; version: string; generators_available: boolean }> {
    return this.request('/api/health');
  }

  // Templates
  async getTemplates(industry?: string): Promise<{ templates: Template[] }> {
    const params = industry ? `?industry=${encodeURIComponent(industry)}` : '';
    return this.request(`/api/v1/templates${params}`);
  }

  async getTemplateById(templateId: string): Promise<Template> {
    return this.request(`/api/v1/templates/${templateId}`);
  }

  // Industries
  async getIndustries(): Promise<{ industries: Industry[] }> {
    return this.request('/api/v1/industries');
  }

  // Generation
  async startGeneration(request: GenerationRequest): Promise<GenerationResponse> {
    return this.request('/api/v1/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getGenerationStatus(generationId: string): Promise<GenerationStatus> {
    return this.request(`/api/v1/generate/${generationId}/status`);
  }

  // Documents
  async generatePreview(templateData: any, brandConfig?: BrandConfig): Promise<{ preview_base64: string }> {
    return this.request('/api/v1/documents/preview', {
      method: 'POST',
      body: JSON.stringify({
        template_data: templateData,
        brand_config: brandConfig,
        preview_mode: true,
      }),
    });
  }

  // Polling utility for generation status
  async pollGenerationStatus(
    generationId: string,
    onUpdate?: (status: GenerationStatus) => void,
    maxAttempts: number = 60,
    intervalMs: number = 2000
  ): Promise<GenerationStatus> {
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const status = await this.getGenerationStatus(generationId);
          
          if (onUpdate) {
            onUpdate(status);
          }

          if (status.status === 'completed') {
            resolve(status);
            return;
          }

          if (status.status === 'failed') {
            reject(new Error(status.error || 'Generation failed'));
            return;
          }

          attempts++;
          if (attempts >= maxAttempts) {
            reject(new Error('Generation timeout'));
            return;
          }

          setTimeout(poll, intervalMs);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}

// Create and export API instance
export const api = new SOPBuilderAPI();

// React Hook for API calls
export function useAPI() {
  return {
    api,
    
    // Convenience methods
    async generateSOP(
      templateId: string,
      companyName: string,
      location?: string,
      selectedOptions: string[] = [],
      llmProvider: string = 'automatic'
    ): Promise<{ generationId: string; pollStatus: () => Promise<GenerationStatus> }> {
      const request: GenerationRequest = {
        template_id: templateId,
        company_info: {
          name: companyName,
          location,
        },
        customization: {
          selected_options: selectedOptions,
        },
        llm_provider: llmProvider,
      };

      const response = await api.startGeneration(request);
      
      return {
        generationId: response.generation_id,
        pollStatus: () => api.pollGenerationStatus(response.generation_id),
      };
    },

    async getTemplatesForIndustry(industry: string): Promise<Template[]> {
      const response = await api.getTemplates(industry);
      return response.templates;
    },

    async getAllTemplates(): Promise<Template[]> {
      const response = await api.getTemplates();
      return response.templates;
    },

    async checkHealth(): Promise<boolean> {
      try {
        const health = await api.healthCheck();
        return health.status === 'healthy';
      } catch {
        return false;
      }
    },
  };
}

// Error handling utility
export class APIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Export types for use in components
export type {
  Template,
  TemplateOption,
  GenerationRequest,
  GenerationResponse,
  GenerationStatus,
  BrandConfig,
  Industry,
};
