#!/bin/bash

# SOP Builder MVP - Production Deployment Script
# Builds frontend and prepares for deployment

set -e  # Exit on any error

echo "🚀 Starting SOP Builder MVP deployment preparation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "deploy.sh" ]; then
    print_error "Please run this script from the sop-templates directory"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Building React frontend for production..."

# Navigate to frontend directory
cd sop-wizard-pro-main

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing npm dependencies..."
    npm install
fi

# Build for production
print_status "Building production bundle..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    print_error "Build failed - dist directory not created"
    exit 1
fi

print_success "Frontend build completed successfully!"

# Go back to root directory
cd ..

# Create deployment package
print_status "Creating deployment package..."

# Create deployment directory
DEPLOY_DIR="deployment-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$DEPLOY_DIR"

# Copy frontend build
print_status "Copying frontend files..."
cp -r sop-wizard-pro-main/dist "$DEPLOY_DIR/frontend"

# Copy backend files
print_status "Copying backend files..."
mkdir -p "$DEPLOY_DIR/backend"
cp -r api "$DEPLOY_DIR/backend/"
cp -r scripts "$DEPLOY_DIR/backend/"

# Copy configuration files
cp requirements.txt "$DEPLOY_DIR/backend/" 2>/dev/null || print_warning "requirements.txt not found"

# Create deployment instructions
cat > "$DEPLOY_DIR/DEPLOYMENT_INSTRUCTIONS.md" << EOF
# SOP Builder MVP - Deployment Package

## Frontend Deployment (Hostinger)

1. **Upload frontend files to Hostinger:**
   - Upload contents of \`frontend/\` folder to \`public_html/sop/\`
   - Ensure \`index.html\` is in the root of the sop directory

2. **Access URL:**
   - https://nextlevelsbs.com/sop/

## Backend Deployment Options

### Option A: Railway (Free Tier)
1. Connect your GitHub repository to Railway
2. Deploy the backend folder
3. Set environment variables in Railway dashboard

### Option B: DigitalOcean Droplet
1. Create Ubuntu 22.04 droplet (\$6/month)
2. Upload backend files to server
3. Install Python dependencies: \`pip install -r requirements.txt\`
4. Run API server: \`python api/simple_server.py\`

### Option C: Render (Free Tier)
1. Connect GitHub repository to Render
2. Create new Web Service
3. Set build command: \`pip install -r requirements.txt\`
4. Set start command: \`python api/simple_server.py\`

## Environment Configuration

Update the API URL in your frontend build if needed:
- Production API URL should be set in \`.env.production\`
- Current setting: https://nextlevelsbs.com

## Testing

1. **Frontend**: https://nextlevelsbs.com/sop/
2. **API Health**: https://your-api-domain.com/api/health
3. **Templates**: https://nextlevelsbs.com/sop/templates

Generated: $(date)
EOF

# Create a simple requirements.txt if it doesn't exist
if [ ! -f "$DEPLOY_DIR/backend/requirements.txt" ]; then
    print_status "Creating requirements.txt..."
    cat > "$DEPLOY_DIR/backend/requirements.txt" << EOF
# SOP Builder MVP - Python Dependencies
requests>=2.31.0
python-dotenv>=1.0.0
tqdm>=4.66.0
reportlab>=4.0.0
Pillow>=10.0.0
EOF
fi

# Create startup script for backend
cat > "$DEPLOY_DIR/backend/start_server.sh" << EOF
#!/bin/bash
# SOP Builder MVP - Server Startup Script

echo "🚀 Starting SOP Builder MVP API Server..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "✅ Virtual environment activated"
fi

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Start the server
echo "🌐 Starting API server on port 8000..."
python api/simple_server.py
EOF

chmod +x "$DEPLOY_DIR/backend/start_server.sh"

# Create archive for easy upload
print_status "Creating deployment archive..."
tar -czf "$DEPLOY_DIR.tar.gz" "$DEPLOY_DIR"

print_success "Deployment package created successfully!"
echo ""
echo "📦 Deployment files ready:"
echo "   📁 Directory: $DEPLOY_DIR/"
echo "   📄 Archive: $DEPLOY_DIR.tar.gz"
echo ""
echo "🌐 Next steps:"
echo "   1. Upload frontend/ contents to Hostinger: public_html/sop/"
echo "   2. Deploy backend/ to your chosen platform"
echo "   3. Update API URL if needed"
echo "   4. Test at: https://nextlevelsbs.com/sop/"
echo ""
print_success "Ready for production deployment! 🚀"
