# SOP Builder MVP - API Specification

## Base URL
- Development: `http://localhost:8000/api/v1`
- Production: `https://api.nextlevelsbs.com/v1`

## Authentication
- JWT-based authentication for user sessions
- API key authentication for external integrations

## Core Endpoints

### 1. Template Management

#### GET /templates
Get available SOP templates by industry
```json
{
  "templates": [
    {
      "id": "restaurant-opening",
      "title": "Restaurant Opening Procedures",
      "description": "Complete checklist for daily restaurant opening procedures",
      "industry": "restaurant",
      "icon": "🍽️",
      "estimated_time": "2-3 minutes",
      "compliance": ["FDA Food Code", "HACCP", "ServSafe"],
      "custom_options": [
        {
          "id": "food-safety",
          "label": "Include Food Safety Protocols",
          "default": true,
          "required": false
        }
      ]
    }
  ]
}
```

#### GET /templates/{template_id}
Get specific template details
```json
{
  "id": "restaurant-opening",
  "title": "Restaurant Opening Procedures",
  "content_sections": ["introduction", "procedures", "compliance"],
  "regulatory_requirements": {
    "fda_food_code": "2022 FDA Food Code Section 2-301.11",
    "haccp": "HACCP Principle 1-7 Implementation"
  }
}
```

### 2. SOP Generation

#### POST /generate
Generate SOP document
```json
{
  "template_id": "restaurant-opening",
  "company_info": {
    "name": "Joe's Restaurant",
    "location": "Austin, TX",
    "industry_specific": {}
  },
  "customization": {
    "selected_options": ["food-safety", "haccp"],
    "brand_config": {
      "primary_color": "#2C3E50",
      "logo_url": "https://example.com/logo.png"
    }
  },
  "llm_provider": "automatic",
  "output_format": "json"
}
```

Response:
```json
{
  "generation_id": "gen_123456",
  "status": "processing",
  "estimated_completion": "2024-01-15T10:30:00Z",
  "websocket_url": "ws://localhost:8000/ws/generation/gen_123456"
}
```

#### GET /generate/{generation_id}/status
Check generation status
```json
{
  "generation_id": "gen_123456",
  "status": "completed",
  "progress": 100,
  "current_step": "PDF generation complete",
  "result": {
    "template_data": {...},
    "pdf_url": "/api/v1/documents/doc_789/download",
    "preview_url": "/api/v1/documents/doc_789/preview"
  }
}
```

### 3. Brand Management

#### POST /brand/upload-logo
Upload company logo
```json
{
  "logo_file": "base64_encoded_image",
  "company_id": "comp_123"
}
```

#### PUT /brand/config
Update brand configuration
```json
{
  "primary_color": "#2C3E50",
  "secondary_color": "#3498DB",
  "company_name": "Joe's Restaurant",
  "tagline": "Quality Food, Quality Service",
  "font_family": "Helvetica"
}
```

### 4. Document Management

#### GET /documents
List generated documents
```json
{
  "documents": [
    {
      "id": "doc_789",
      "title": "Restaurant Opening Procedures - Joe's Restaurant",
      "template_id": "restaurant-opening",
      "created_at": "2024-01-15T10:30:00Z",
      "status": "completed",
      "file_size": 245760,
      "download_count": 3
    }
  ]
}
```

#### GET /documents/{document_id}/download
Download PDF document

#### GET /documents/{document_id}/preview
Get PDF preview (base64 encoded)

### 5. Compliance Validation

#### POST /compliance/validate
Validate SOP against regulations
```json
{
  "template_data": {...},
  "industry": "restaurant",
  "regulations": ["FDA_FOOD_CODE_2022", "HACCP"]
}
```

Response:
```json
{
  "validation_result": {
    "compliant": true,
    "missing_requirements": [],
    "recommendations": [
      "Consider adding temperature monitoring procedures"
    ],
    "regulatory_citations": [
      {
        "regulation": "FDA Food Code 2022",
        "section": "2-301.11",
        "requirement": "Hand washing procedures"
      }
    ]
  }
}
```

### 6. Industry Templates

#### GET /industries
Get available industries
```json
{
  "industries": [
    {
      "id": "restaurant",
      "name": "Restaurant & Food Service",
      "template_count": 12,
      "compliance_standards": ["FDA Food Code", "HACCP", "ServSafe"]
    }
  ]
}
```

#### GET /industries/{industry_id}/templates
Get templates for specific industry

### 7. Real-time Updates

#### WebSocket: /ws/generation/{generation_id}
Real-time generation progress updates
```json
{
  "type": "progress_update",
  "generation_id": "gen_123456",
  "progress": 45,
  "current_step": "Generating procedure steps...",
  "estimated_remaining": "30 seconds"
}
```

## Error Handling

All endpoints return standardized error responses:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Company name is required",
    "details": {
      "field": "company_info.name",
      "constraint": "required"
    }
  }
}
```

## Rate Limiting
- 100 requests per minute per IP
- 10 generation requests per hour per user
- Burst allowance: 20 requests in 10 seconds

## Pagination
Standard pagination for list endpoints:
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 150,
    "total_pages": 8
  }
}
```
