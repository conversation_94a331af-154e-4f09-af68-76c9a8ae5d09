
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Clock, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import Layout from "@/components/Layout";
import { useAPI, type Template, type GenerationStatus } from "@/services/api";

const Generate = () => {
  const { templateId } = useParams();
  const { api } = useAPI();

  // State management
  const [template, setTemplate] = useState<Template | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStep, setGenerationStep] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [location, setLocation] = useState("");
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [generationResult, setGenerationResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Load template data from API
  useEffect(() => {
    const loadTemplate = async () => {
      if (!templateId) return;

      try {
        const templateData = await api.getTemplateById(templateId);
        setTemplate(templateData);

        // Set default selected options
        const defaultOptions = templateData.custom_options
          .filter(option => option.default)
          .map(option => option.id);
        setSelectedOptions(defaultOptions);

      } catch (error) {
        console.error('Failed to load template:', error);
        setError('Failed to load template. Please try again.');
      }
    };

    loadTemplate();
  }, [templateId, api]);

  const llmProviders = [
    { id: "automatic", name: "Automatic (Recommended)", description: "Tries Groq → Hugging Face → Together AI → OpenRouter" },
    { id: "groq", name: "Groq", description: "llama-3.1-70b-versatile (fastest, free tier)" },
    { id: "huggingface", name: "Hugging Face", description: "300 requests/hour free" },
    { id: "together", name: "Together AI", description: "$25 free credits" },
    { id: "openrouter", name: "OpenRouter", description: "deepseek-chat (free tier)" }
  ];

  const [selectedProvider, setSelectedProvider] = useState("automatic");

  const handleOptionToggle = (optionId: string) => {
    setSelectedOptions(prev => 
      prev.includes(optionId) 
        ? prev.filter(id => id !== optionId)
        : [...prev, optionId]
    );
  };

  const handleGenerate = async () => {
    if (!template || !companyName.trim()) return;

    setIsGenerating(true);
    setGenerationProgress(0);
    setGenerationStep("Starting generation...");
    setError(null);

    try {
      // Start generation
      const response = await api.startGeneration({
        template_id: template.id,
        company_info: {
          name: companyName.trim(),
          location: location.trim() || undefined,
        },
        customization: {
          selected_options: selectedOptions,
        },
        llm_provider: selectedProvider,
      });

      // Poll for status updates
      await api.pollGenerationStatus(
        response.generation_id,
        (status: GenerationStatus) => {
          setGenerationProgress(status.progress);
          setGenerationStep(status.current_step || "Processing...");

          if (status.status === 'completed' && status.result) {
            setGenerationResult(status.result);
            setIsGenerating(false);
          }
        }
      );

    } catch (error) {
      console.error('Generation failed:', error);
      setError(error instanceof Error ? error.message : 'Generation failed. Please try again.');
      setIsGenerating(false);
      setGenerationStep("");
    }
  };

  // Show loading state
  if (!template) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link to="/templates">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Templates
              </Link>
            </Button>
          </div>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#3498DB] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading template...</p>
            {error && (
              <p className="mt-2 text-red-600">{error}</p>
            )}
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" asChild>
            <Link to="/templates">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Templates
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Template Info Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader className="text-center">
                <div className="text-4xl mb-2">{template.icon}</div>
                <CardTitle className="text-lg">{template.title}</CardTitle>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>Est. Time</span>
                  </span>
                  <span className="font-medium">{template.estimated_time}</span>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-2 block">Compliance Standards</Label>
                  <div className="flex flex-wrap gap-1">
                    {template.compliance.map((standard) => (
                      <Badge key={standard} variant="secondary" className="text-xs">
                        {standard}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center space-x-2 text-green-600">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">100% Free Generation</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    No subscription required. Uses free AI providers.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Generation Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Generate {template.title}</CardTitle>
                <CardDescription>
                  Customize your SOP template with company-specific information and preferences.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Company Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Company Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="companyName">Company Name *</Label>
                      <Input
                        id="companyName"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        placeholder="Enter your company name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={location}
                        onChange={(e) => setLocation(e.target.value)}
                        placeholder="City, State"
                      />
                    </div>
                  </div>
                </div>

                {/* Customization Options */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Customization Options</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {template.custom_options.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={option.id}
                          checked={selectedOptions.includes(option.id)}
                          onCheckedChange={() => handleOptionToggle(option.id)}
                        />
                        <Label htmlFor={option.id} className="text-sm">
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* LLM Provider Selection */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">AI Provider Selection</h3>
                  <div className="space-y-2">
                    {llmProviders.map((provider) => (
                      <div key={provider.id} className="flex items-center space-x-3">
                        <input
                          type="radio"
                          id={provider.id}
                          name="llmProvider"
                          value={provider.id}
                          checked={selectedProvider === provider.id}
                          onChange={(e) => setSelectedProvider(e.target.value)}
                          className="w-4 h-4 text-[#3498DB]"
                        />
                        <div className="flex-1">
                          <Label htmlFor={provider.id} className="font-medium">
                            {provider.name}
                          </Label>
                          <p className="text-xs text-gray-600">{provider.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Error Display */}
                {error && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center space-x-2 text-red-700">
                      <span className="font-medium">Generation Failed</span>
                    </div>
                    <p className="text-sm text-red-600 mt-1">{error}</p>
                  </div>
                )}

                {/* Generation Progress */}
                {isGenerating && (
                  <div className="space-y-4 p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-[#3498DB] animate-pulse" />
                      <span className="font-medium">Generating your SOP...</span>
                    </div>
                    <Progress value={generationProgress} className="w-full" />
                    <p className="text-sm text-gray-600">{generationStep}</p>
                  </div>
                )}

                {/* Generation Success */}
                {generationResult && !isGenerating && (
                  <div className="space-y-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-2 text-green-700">
                      <CheckCircle className="w-5 h-5" />
                      <span className="font-medium">SOP Generated Successfully!</span>
                    </div>
                    <p className="text-sm text-green-600">
                      Your SOP has been generated and is ready for download.
                    </p>
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Download PDF
                    </Button>
                  </div>
                )}

                {/* Generate Button */}
                <div className="pt-4">
                  <Button
                    onClick={handleGenerate}
                    disabled={!companyName || isGenerating}
                    className="w-full bg-[#3498DB] hover:bg-[#2980B9] text-white py-3"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Zap className="w-4 h-4 mr-2 animate-pulse" />
                        Generating SOP Template...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        Generate SOP Template
                      </>
                    )}
                  </Button>
                  {!companyName && (
                    <p className="text-sm text-gray-500 mt-2">
                      Please enter your company name to continue.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Generate;
