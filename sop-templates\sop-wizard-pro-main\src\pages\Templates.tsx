
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Search, Filter, Star, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import Layout from "@/components/Layout";

const Templates = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Templates", count: 48 },
    { id: "restaurant", name: "Restaurant", count: 12 },
    { id: "healthcare", name: "Healthcare", count: 18 },
    { id: "it-onboarding", name: "IT Onboarding", count: 8 },
    { id: "customer-service", name: "Customer Service", count: 10 }
  ];

  const templates = [
    {
      id: "restaurant-opening",
      title: "Restaurant Opening Procedures",
      description: "Complete checklist for daily restaurant opening procedures including equipment checks, food safety, and staff preparation.",
      category: "restaurant",
      compliance: ["FDA Food Code", "HACCP", "ServSafe"],
      rating: 4.8,
      downloads: 1247,
      estimatedTime: "2-3 minutes",
      icon: "🍽️"
    },
    {
      id: "healthcare-patient-intake",
      title: "Patient Intake Procedures",
      description: "Comprehensive patient intake process including registration, insurance verification, and HIPAA compliance procedures.",
      category: "healthcare",
      compliance: ["HIPAA", "CDC Guidelines", "Joint Commission"],
      rating: 4.9,
      downloads: 2134,
      estimatedTime: "3-4 minutes",
      icon: "🏥"
    },
    {
      id: "it-employee-onboarding",
      title: "IT Employee Onboarding",
      description: "Complete IT onboarding process including account setup, security training, and system access procedures.",
      category: "it-onboarding",
      compliance: ["SOX", "ISO 27001", "GDPR"],
      rating: 4.7,
      downloads: 856,
      estimatedTime: "4-5 minutes",
      icon: "💻"
    },
    {
      id: "customer-service-escalation",
      title: "Customer Service Escalation",
      description: "Escalation procedures for customer service issues including severity levels and manager notification protocols.",
      category: "customer-service",
      compliance: ["ISO 9001", "CSAT Standards"],
      rating: 4.6,
      downloads: 934,
      estimatedTime: "2-3 minutes",
      icon: "📞"
    },
    {
      id: "restaurant-cleaning",
      title: "Deep Cleaning Procedures",
      description: "Detailed cleaning and sanitization procedures for restaurant equipment, surfaces, and dining areas.",
      category: "restaurant",
      compliance: ["FDA Food Code", "Local Health Dept"],
      rating: 4.8,
      downloads: 1156,
      estimatedTime: "3-4 minutes",
      icon: "🧽"
    },
    {
      id: "healthcare-emergency",
      title: "Medical Emergency Response",
      description: "Emergency response procedures for medical facilities including code team activation and patient stabilization.",
      category: "healthcare",
      compliance: ["Joint Commission", "AHA Guidelines"],
      rating: 4.9,
      downloads: 1678,
      estimatedTime: "3-4 minutes",
      icon: "🚨"
    }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-[#2C3E50] mb-2">SOP Template Library</h1>
          <p className="text-gray-600">Choose from our professionally crafted SOP templates, all with built-in compliance standards.</p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" className="md:w-auto">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.id)}
              className={`${
                selectedCategory === category.id 
                  ? "bg-[#3498DB] hover:bg-[#2980B9]" 
                  : ""
              }`}
            >
              {category.name} ({category.count})
            </Button>
          ))}
        </div>

        {/* Template Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="text-3xl mb-2">{template.icon}</div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{template.rating}</span>
                  </div>
                </div>
                <CardTitle className="text-lg leading-tight">{template.title}</CardTitle>
                <CardDescription className="text-sm">{template.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-1">
                  {template.compliance.map((standard) => (
                    <Badge key={standard} variant="secondary" className="text-xs">
                      {standard}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>{template.downloads.toLocaleString()} downloads</span>
                  </div>
                  <span>Est. {template.estimatedTime}</span>
                </div>

                <div className="flex space-x-2">
                  <Button asChild className="flex-1 bg-[#3498DB] hover:bg-[#2980B9]">
                    <Link to={`/generate/${template.id}`}>Generate SOP</Link>
                  </Button>
                  <Button variant="outline" size="icon">
                    <Star className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Results Summary */}
        <div className="text-center text-gray-600">
          Showing {filteredTemplates.length} of {templates.length} templates
        </div>
      </div>
    </Layout>
  );
};

export default Templates;
