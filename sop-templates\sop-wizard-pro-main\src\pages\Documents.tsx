
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Search, Download, Eye, Trash2, Calendar, FileText } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import Layout from "@/components/Layout";

const Documents = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const documents = [
    {
      id: "doc-001",
      title: "Restaurant Opening Procedures - Bella Vista",
      templateType: "Restaurant Operations",
      createdDate: "2025-01-29",
      provider: "Groq",
      model: "llama-3.1-70b-versatile",
      fileSize: "2.3 MB",
      status: "completed",
      downloadCount: 5,
      icon: "🍽️"
    },
    {
      id: "doc-002", 
      title: "Healthcare Patient Intake - MedCenter Pro",
      templateType: "Healthcare Procedures",
      createdDate: "2025-01-28",
      provider: "Hugging Face",
      model: "Open Source Model",
      fileSize: "3.1 MB",
      status: "completed",
      downloadCount: 2,
      icon: "🏥"
    },
    {
      id: "doc-003",
      title: "IT Employee Onboarding - TechCorp Solutions",
      templateType: "IT Onboarding",
      createdDate: "2025-01-27",
      provider: "Together AI",
      model: "Free Tier",
      fileSize: "2.8 MB",
      status: "completed", 
      downloadCount: 8,
      icon: "💻"
    },
    {
      id: "doc-004",
      title: "Customer Service Escalation - ServiceFirst Inc",
      templateType: "Customer Service",
      createdDate: "2025-01-26",
      provider: "OpenRouter",
      model: "deepseek-chat",
      fileSize: "1.9 MB",
      status: "completed",
      downloadCount: 3,
      icon: "📞"
    },
    {
      id: "doc-005",
      title: "Restaurant Cleaning Procedures - Quick Bites",
      templateType: "Restaurant Operations", 
      createdDate: "2025-01-25",
      provider: "Groq",
      model: "llama-3.1-70b-versatile",
      fileSize: "2.7 MB",
      status: "completed",
      downloadCount: 12,
      icon: "🧽"
    }
  ];

  const filteredDocuments = documents.filter(doc => 
    doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.templateType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getProviderColor = (provider: string) => {
    const colors = {
      "Groq": "bg-purple-100 text-purple-800",
      "Hugging Face": "bg-yellow-100 text-yellow-800", 
      "Together AI": "bg-green-100 text-green-800",
      "OpenRouter": "bg-blue-100 text-blue-800"
    };
    return colors[provider as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-[#2C3E50] mb-2">Generated Documents</h1>
            <p className="text-gray-600">View and manage your generated SOP documents.</p>
          </div>
          <Button asChild className="bg-[#3498DB] hover:bg-[#2980B9]">
            <Link to="/templates">
              Create New SOP
            </Link>
          </Button>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Documents Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredDocuments.map((doc) => (
            <Card key={doc.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="text-2xl mb-2">{doc.icon}</div>
                  <Badge className={getProviderColor(doc.provider)}>
                    {doc.provider}
                  </Badge>
                </div>
                <CardTitle className="text-lg leading-tight line-clamp-2">
                  {doc.title}
                </CardTitle>
                <CardDescription className="text-sm">
                  {doc.templateType}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(doc.createdDate).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <FileText className="w-4 h-4" />
                    <span>{doc.fileSize}</span>
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  Generated with {doc.model}
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    Downloaded {doc.downloadCount} time{doc.downloadCount !== 1 ? 's' : ''}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {doc.status}
                  </Badge>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="w-4 h-4 mr-1" />
                    Preview
                  </Button>
                  <Button size="sm" className="flex-1 bg-[#3498DB] hover:bg-[#2980B9]">
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredDocuments.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? "Try adjusting your search criteria." : "You haven't generated any SOPs yet."}
            </p>
            <Button asChild className="bg-[#3498DB] hover:bg-[#2980B9]">
              <Link to="/templates">
                Generate Your First SOP
              </Link>
            </Button>
          </div>
        )}

        {/* Results Summary */}
        {filteredDocuments.length > 0 && (
          <div className="text-center text-gray-600">
            Showing {filteredDocuments.length} of {documents.length} documents
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Documents;
