"""
Makefile for SOP Template Automation
Simplifies common tasks to single commands
"""

# Create make.py for Windows compatibility
import os
import sys
import subprocess
import argparse
from datetime import datetime

def run_command(cmd):
    """Run a command and return the result"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False
    print(result.stdout)
    return True

def setup():
    """Initial setup of the system"""
    commands = [
        "pip install -r requirements.txt",
        "mkdir -p outputs/staging outputs/reports outputs/pdfs outputs/videos outputs/templates",
        "mkdir -p designs/assets data/compliance templates",
        "cp .env.example .env"
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            print("Setup failed!")
            return
    
    print("✅ Setup complete! Don't forget to update .env with your API keys.")

def generate(template_type="all"):
    """Generate SOP templates"""
    if template_type == "all":
        types = ["restaurant", "healthcare", "it-onboarding", "customer-service"]
    else:
        types = [template_type]
    
    for t in types:
        cmd = f"python scripts/generators/sop_generator.py --type {t}"
        run_command(cmd)

def pdf(template_type="all"):
    """Generate PDFs from templates"""
    if template_type == "all":
        # Convert all JSON templates to PDFs
        import glob
        for json_file in glob.glob("outputs/templates/*.json"):
            cmd = f"python scripts/generators/pdf_generator.py --input {json_file}"
            run_command(cmd)
    else:
        # Convert specific template
        json_file = f"outputs/templates/{template_type}_*.json"
        cmd = f"python scripts/generators/pdf_generator.py --input {json_file}"
        run_command(cmd)

def video(template_type):
    """Generate promotional video"""
    cmd = f"python scripts/generators/video_generator.py --type {template_type} --method auto"
    run_command(cmd)

def update():
    """Run daily update check"""
    cmd = "python scripts/automation/daily_update.py"
    run_command(cmd)

def deploy(test=False):
    """Deploy templates to Gumroad"""
    cmd = "python scripts/automation/deploy.py"
    if test:
        cmd += " --test"
    
    # Find latest staging file
    import glob
    staging_files = sorted(glob.glob("outputs/staging/updates_*.json"))
    if staging_files:
        cmd += f" --staging-file {staging_files[-1]}"
    
    run_command(cmd)

def daily():
    """Run complete daily workflow"""
    print("🚀 Starting daily workflow...")
    
    # 1. Check for updates
    print("\n📊 Checking for compliance updates...")
    update()
    
    # 2. Generate any needed templates
    print("\n📝 Generating updated templates...")
    generate()
    
    # 3. Convert to PDFs
    print("\n📄 Creating PDFs...")
    pdf()
    
    # 4. Deploy
    print("\n🚢 Deploying to Gumroad...")
    deploy()
    
    print("\n✅ Daily workflow complete!")

def clean():
    """Clean temporary files"""
    patterns = [
        "outputs/staging/temp_*",
        "outputs/videos/temp_*",
        "*.log",
        "__pycache__"
    ]
    
    for pattern in patterns:
        cmd = f"rm -rf {pattern}"
        run_command(cmd)
    
    print("✅ Cleanup complete!")

def test():
    """Run tests"""
    cmd = "pytest tests/"
    run_command(cmd)

def main():
    parser = argparse.ArgumentParser(description='SOP Template Automation Tasks')
    parser.add_argument('task', choices=[
        'setup', 'generate', 'pdf', 'video', 'update', 
        'deploy', 'daily', 'clean', 'test'
    ], help='Task to run')
    parser.add_argument('--type', help='Template type (restaurant, healthcare, etc.)')
    parser.add_argument('--test', action='store_true', help='Run in test mode')
    
    args = parser.parse_args()
    
    if args.task == 'setup':
        setup()
    elif args.task == 'generate':
        generate(args.type or 'all')
    elif args.task == 'pdf':
        pdf(args.type or 'all')
    elif args.task == 'video':
        if not args.type:
            print("Error: --type required for video generation")
            return
        video(args.type)
    elif args.task == 'update':
        update()
    elif args.task == 'deploy':
        deploy(args.test)
    elif args.task == 'daily':
        daily()
    elif args.task == 'clean':
        clean()
    elif args.task == 'test':
        test()

if __name__ == "__main__":
    main()
