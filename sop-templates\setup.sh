#!/bin/bash
# Quick setup script for the SOP Template system

echo "🚀 Setting up SOP Template Automation System..."

# Create necessary directories
echo "Creating directory structure..."
mkdir -p outputs/staging
mkdir -p outputs/reports
mkdir -p outputs/pdfs
mkdir -p outputs/videos
mkdir -p outputs/templates
mkdir -p designs/assets
mkdir -p data/compliance
mkdir -p templates

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Create default brand config
echo "Creating default brand configuration..."
cat > config/brand_config.json << EOF
{
  "primary_color": "#2C3E50",
  "secondary_color": "#3498DB",
  "accent_color": "#E74C3C",
  "logo_path": "designs/assets/logo.png",
  "font_family": "Helvetica",
  "company_name": "Premium SOP Templates",
  "tagline": "Compliance Made Simple"
}
EOF

# Create sample compliance data files
echo "Creating sample compliance data..."

# Healthcare compliance data
cat > data/compliance/healthcare.yaml << EOF
standards:
  - HIPAA Privacy Rule
  - HIPAA Security Rule
  - HITECH Act
  - State Privacy Laws

regulatory_links:
  HHS: "https://www.hhs.gov/hipaa/for-professionals/index.html"
  OCR: "https://www.hhs.gov/ocr/index.html"

sections:
  - name: privacy_procedures
    order: 1
    required: true
    has_checklist: true
    checklist_items:
      - "Designate Privacy Officer"
      - "Implement minimum necessary standards"
      - "Create Notice of Privacy Practices"
      - "Obtain patient authorizations"
      
  - name: security_safeguards
    order: 2
    required: true
    has_checklist: true
    checklist_items:
      - "Conduct risk assessment"
      - "Implement access controls"
      - "Encrypt ePHI"
      - "Create incident response plan"
EOF

# IT Onboarding compliance data
cat > data/compliance/it-onboarding.yaml << EOF
standards:
  - ISO 27001
  - SOC 2
  - NIST Cybersecurity Framework
  - Zero Trust Architecture

regulatory_links:
  NIST: "https://www.nist.gov/cyberframework"
  ISO: "https://www.iso.org/isoiec-27001-information-security.html"

sections:
  - name: access_provisioning
    order: 1
    required: true
    has_checklist: true
    checklist_items:
      - "Create user account"
      - "Assign appropriate permissions"
      - "Enable MFA"
      - "Configure VPN access"
      
  - name: security_training
    order: 2
    required: true
    has_checklist: true
    checklist_items:
      - "Security awareness training"
      - "Phishing simulation"
      - "Data handling procedures"
      - "Incident reporting"
EOF

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cp .env.example .env
    echo "⚠️  Please update .env with your API keys"
fi

# Create initial setup marker
touch .setup_complete

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update .env with your API keys"
echo "2. Add your logo to designs/assets/logo.png"
echo "3. Run: python scripts/generators/sop_generator.py --type restaurant"
echo "4. Run: python scripts/automation/daily_update.py"
echo ""
echo "Happy automating! 🎉"
